import { addDays, differenceInDays, isWeekend, format } from "date-fns";
import { 
	ICalculationService, 
	IWorkingDaysCalculation, 
	ICalculationResult, 
	IPeriodStats,
	IHoliday 
} from "@/types";
import { holidayService } from "./holidayService";

class CalculationService implements ICalculationService {
	/**
	 * Calcula a data final baseada em dias úteis
	 */
	calculateEndDate(calculation: IWorkingDaysCalculation): ICalculationResult {
		const { 
			startDate, 
			workingDays, 
			includeStartDate = true, 
			excludeHolidays = true,
			customWorkingDays = [1, 2, 3, 4, 5] // Segunda a Sexta por padrão
		} = calculation;

		if (!startDate || workingDays <= 0) {
			throw new Error("Data inicial e quantidade de dias úteis são obrigatórios");
		}

		let currentDate = new Date(startDate);
		let daysAdded = 0;
		let totalDaysAdded = 0;
		let weekendsSkipped = 0;
		let holidaysSkipped = 0;

		// Se não incluir data inicial, começar do próximo dia
		if (!includeStartDate) {
			currentDate = addDays(currentDate, 1);
			totalDaysAdded = 1;
		}

		while (daysAdded < workingDays) {
			const dayOfWeek = currentDate.getDay();
			const isWorkingDay = customWorkingDays.includes(dayOfWeek);
			const isHolidayDate = excludeHolidays && holidayService.isHoliday(currentDate);

			if (isWorkingDay && !isHolidayDate) {
				daysAdded++;
			} else if (!isWorkingDay) {
				weekendsSkipped++;
			} else if (isHolidayDate) {
				holidaysSkipped++;
			}

			// Se ainda não chegamos ao número desejado, avançar para o próximo dia
			if (daysAdded < workingDays) {
				currentDate = addDays(currentDate, 1);
				totalDaysAdded++;
			}
		}

		const endDate = currentDate;
		const periodStats = this.calculateWorkingDaysBetween(startDate, endDate);

		return {
			endDate,
			periodStats,
			calculationDetails: {
				totalDaysAdded,
				weekendsSkipped,
				holidaysSkipped,
				actualWorkingDays: daysAdded,
			},
		};
	}

	/**
	 * Calcula estatísticas de dias úteis entre duas datas
	 */
	calculateWorkingDaysBetween(startDate: Date, endDate: Date): IPeriodStats {
		if (startDate > endDate) {
			throw new Error("Data inicial deve ser anterior à data final");
		}

		const totalDays = differenceInDays(endDate, startDate) + 1;
		let workingDays = 0;
		let weekends = 0;
		let holidays = 0;
		const holidaysList: IHoliday[] = [];

		let currentDate = new Date(startDate);

		for (let i = 0; i < totalDays; i++) {
			const dayOfWeek = currentDate.getDay();
			const isWeekendDay = isWeekend(currentDate);
			const holidayName = holidayService.getHolidayName(currentDate);

			if (holidayName) {
				holidays++;
				holidaysList.push({
					date: format(currentDate, "yyyy-MM-dd"),
					name: holidayName,
					type: "nacional", // Simplificado por enquanto
				});
			} else if (isWeekendDay) {
				weekends++;
			} else {
				workingDays++;
			}

			currentDate = addDays(currentDate, 1);
		}

		return {
			totalDays,
			workingDays,
			weekends,
			holidays,
			holidaysList,
		};
	}

	/**
	 * Calcula dias úteis com opções simplificadas
	 */
	calculateBusinessDays(
		startDate: Date, 
		days: number, 
		options: Partial<IWorkingDaysCalculation> = {}
	): Date {
		const calculation: IWorkingDaysCalculation = {
			startDate,
			workingDays: days,
			includeStartDate: true,
			excludeHolidays: true,
			customWorkingDays: [1, 2, 3, 4, 5],
			...options,
		};

		const result = this.calculateEndDate(calculation);
		return result.endDate;
	}

	/**
	 * Calcula múltiplos cenários de uma vez
	 */
	calculateMultipleScenarios(
		baseCalculation: IWorkingDaysCalculation,
		scenarios: Array<{ name: string; modifier: Partial<IWorkingDaysCalculation> }>
	): Array<{ name: string; result: ICalculationResult }> {
		return scenarios.map(scenario => ({
			name: scenario.name,
			result: this.calculateEndDate({ ...baseCalculation, ...scenario.modifier }),
		}));
	}

	/**
	 * Calcula estimativa com buffer de segurança
	 */
	calculateWithBuffer(
		calculation: IWorkingDaysCalculation,
		bufferPercentage: number = 20
	): { 
		original: ICalculationResult; 
		withBuffer: ICalculationResult; 
		bufferDays: number 
	} {
		const original = this.calculateEndDate(calculation);
		const bufferDays = Math.ceil(calculation.workingDays * (bufferPercentage / 100));
		
		const withBuffer = this.calculateEndDate({
			...calculation,
			workingDays: calculation.workingDays + bufferDays,
		});

		return {
			original,
			withBuffer,
			bufferDays,
		};
	}

	/**
	 * Calcula retroativamente (data inicial baseada na final)
	 */
	calculateStartDate(
		endDate: Date,
		workingDays: number,
		options: Partial<IWorkingDaysCalculation> = {}
	): Date {
		const { 
			excludeHolidays = true,
			customWorkingDays = [1, 2, 3, 4, 5]
		} = options;

		let currentDate = new Date(endDate);
		let daysSubtracted = 0;

		while (daysSubtracted < workingDays) {
			currentDate = addDays(currentDate, -1);
			
			const dayOfWeek = currentDate.getDay();
			const isWorkingDay = customWorkingDays.includes(dayOfWeek);
			const isHolidayDate = excludeHolidays && holidayService.isHoliday(currentDate);

			if (isWorkingDay && !isHolidayDate) {
				daysSubtracted++;
			}
		}

		return currentDate;
	}

	/**
	 * Valida se uma data é dia útil
	 */
	isWorkingDay(
		date: Date, 
		customWorkingDays: number[] = [1, 2, 3, 4, 5],
		excludeHolidays: boolean = true
	): boolean {
		const dayOfWeek = date.getDay();
		const isWorkingDay = customWorkingDays.includes(dayOfWeek);
		const isHolidayDate = excludeHolidays && holidayService.isHoliday(date);

		return isWorkingDay && !isHolidayDate;
	}

	/**
	 * Encontra o próximo dia útil
	 */
	getNextWorkingDay(
		date: Date,
		customWorkingDays: number[] = [1, 2, 3, 4, 5],
		excludeHolidays: boolean = true
	): Date {
		let nextDay = addDays(date, 1);
		
		while (!this.isWorkingDay(nextDay, customWorkingDays, excludeHolidays)) {
			nextDay = addDays(nextDay, 1);
		}
		
		return nextDay;
	}

	/**
	 * Encontra o dia útil anterior
	 */
	getPreviousWorkingDay(
		date: Date,
		customWorkingDays: number[] = [1, 2, 3, 4, 5],
		excludeHolidays: boolean = true
	): Date {
		let previousDay = addDays(date, -1);
		
		while (!this.isWorkingDay(previousDay, customWorkingDays, excludeHolidays)) {
			previousDay = addDays(previousDay, -1);
		}
		
		return previousDay;
	}
}

export const calculationService = new CalculationService();

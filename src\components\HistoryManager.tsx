"use client";

import React, { useState } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { 
	History, 
	Search, 
	Filter, 
	Trash2, 
	Download, 
	Upload,
	Calendar,
	Clock,
	Tag,
	Bar<PERSON>hart3,
	Settings,
	X,
	Plus,
	Eye,
	Edit
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { useHistory } from "@/hooks/useHistory";
import { ICalculationHistory, IHistoryFilter } from "@/types";

interface IHistoryManagerProps {
	onSelectCalculation?: (calculation: ICalculationHistory) => void;
	onCalculationReuse?: (calculation: ICalculationHistory) => void;
}

export function HistoryManager({
	onSelectCalculation,
	onCalculationReuse
}: IHistoryManagerProps) {
	const [searchText, setSearchText] = useState("");
	const [selectedTags, setSelectedTags] = useState<string[]>([]);
	const [showFilters, setShowFilters] = useState(false);
	const [selectedItems, setSelectedItems] = useState<string[]>([]);
	const [editingTags, setEditingTags] = useState<string | null>(null);
	const [newTag, setNewTag] = useState("");
	
	const {
		history,
		filteredHistory,
		stats,
		preferences,
		isLoading,
		error,
		currentFilter,
		removeHistoryItem,
		clearHistory,
		updateItemTags,
		applyFilter,
		clearFilter,
		exportHistory,
		importHistory
	} = useHistory();

	// Aplicar filtros
	const handleApplyFilter = () => {
		const filter: IHistoryFilter = {
			searchText: searchText || undefined,
			tags: selectedTags.length > 0 ? selectedTags : undefined,
		};
		applyFilter(filter);
	};

	// Limpar filtros
	const handleClearFilter = () => {
		setSearchText("");
		setSelectedTags([]);
		clearFilter();
	};

	// Selecionar/deselecionar item
	const handleItemSelect = (id: string) => {
		setSelectedItems(prev => 
			prev.includes(id) 
				? prev.filter(item => item !== id)
				: [...prev, id]
		);
	};

	// Selecionar todos os itens
	const handleSelectAll = () => {
		if (selectedItems.length === filteredHistory.length) {
			setSelectedItems([]);
		} else {
			setSelectedItems(filteredHistory.map(item => item.id));
		}
	};

	// Remover itens selecionados
	const handleRemoveSelected = async () => {
		for (const id of selectedItems) {
			await removeHistoryItem(id);
		}
		setSelectedItems([]);
	};

	// Adicionar tag a um item
	const handleAddTag = async (itemId: string) => {
		if (!newTag.trim()) return;
		
		const item = filteredHistory.find(h => h.id === itemId);
		if (!item) return;
		
		const updatedTags = [...(item.tags || []), newTag.trim()];
		await updateItemTags(itemId, updatedTags);
		setNewTag("");
		setEditingTags(null);
	};

	// Remover tag de um item
	const handleRemoveTag = async (itemId: string, tagToRemove: string) => {
		const item = filteredHistory.find(h => h.id === itemId);
		if (!item) return;
		
		const updatedTags = (item.tags || []).filter(tag => tag !== tagToRemove);
		await updateItemTags(itemId, updatedTags);
	};

	// Importar arquivo de backup
	const handleImportFile = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (!file) return;
		
		const reader = new FileReader();
		reader.onload = async (e) => {
			const content = e.target?.result as string;
			if (content) {
				await importHistory(content);
			}
		};
		reader.readAsText(file);
	};

	// Obter todas as tags únicas
	const allTags = Array.from(
		new Set(history.flatMap(item => item.tags || []))
	).sort();

	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<History className="h-6 w-6" />
					Histórico de Cálculos
				</CardTitle>
				<CardDescription>
					Gerencie e analise seus cálculos anteriores
				</CardDescription>
			</CardHeader>

			<CardContent>
				<Tabs defaultValue="history" className="space-y-6">
					<TabsList className="grid w-full grid-cols-3">
						<TabsTrigger value="history">Histórico</TabsTrigger>
						<TabsTrigger value="stats">Estatísticas</TabsTrigger>
						<TabsTrigger value="settings">Configurações</TabsTrigger>
					</TabsList>

					{/* Aba do Histórico */}
					<TabsContent value="history" className="space-y-4">
						{/* Barra de busca e filtros */}
						<div className="space-y-4">
							<div className="flex gap-2">
								<div className="flex-1 relative">
									<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
									<Input
										placeholder="Buscar no histórico..."
										value={searchText}
										onChange={(e) => setSearchText(e.target.value)}
										className="pl-10"
									/>
								</div>
								<Button
									variant="outline"
									onClick={() => setShowFilters(!showFilters)}
									className="flex items-center gap-2"
								>
									<Filter className="h-4 w-4" />
									Filtros
								</Button>
								<Button onClick={handleApplyFilter}>
									Aplicar
								</Button>
							</div>

							{/* Filtros expandidos */}
							{showFilters && (
								<Card>
									<CardContent className="pt-6">
										<div className="space-y-4">
											<div>
												<Label>Tags</Label>
												<div className="flex flex-wrap gap-2 mt-2">
													{allTags.map(tag => (
														<Badge
															key={tag}
															variant={selectedTags.includes(tag) ? "default" : "outline"}
															className="cursor-pointer"
															onClick={() => {
																setSelectedTags(prev => 
																	prev.includes(tag)
																		? prev.filter(t => t !== tag)
																		: [...prev, tag]
																);
															}}
														>
															{tag}
														</Badge>
													))}
												</div>
											</div>
											
											<div className="flex gap-2">
												<Button variant="outline" onClick={handleClearFilter}>
													Limpar Filtros
												</Button>
											</div>
										</div>
									</CardContent>
								</Card>
							)}
						</div>

						{/* Ações em lote */}
						{filteredHistory.length > 0 && (
							<div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
								<div className="flex items-center gap-2">
									<Checkbox
										checked={selectedItems.length === filteredHistory.length}
										onCheckedChange={handleSelectAll}
									/>
									<span className="text-sm">
										{selectedItems.length > 0 
											? `${selectedItems.length} selecionados`
											: `${filteredHistory.length} itens`
										}
									</span>
								</div>
								
								{selectedItems.length > 0 && (
									<div className="flex gap-2">
										<Button
											size="sm"
											variant="destructive"
											onClick={handleRemoveSelected}
											className="flex items-center gap-1"
										>
											<Trash2 className="h-3 w-3" />
											Remover
										</Button>
									</div>
								)}
							</div>
						)}

						{/* Lista do histórico */}
						<div className="space-y-3">
							{isLoading ? (
								<div className="text-center py-8">
									<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
									<p className="text-sm text-muted-foreground">Carregando histórico...</p>
								</div>
							) : filteredHistory.length === 0 ? (
								<Alert>
									<History className="h-4 w-4" />
									<AlertTitle>Nenhum cálculo encontrado</AlertTitle>
									<AlertDescription>
										{history.length === 0 
											? "Realize alguns cálculos para ver o histórico aqui."
											: "Nenhum cálculo corresponde aos filtros aplicados."
										}
									</AlertDescription>
								</Alert>
							) : (
								filteredHistory.map((item) => (
									<Card key={item.id} className="relative">
										<CardContent className="pt-6">
											<div className="flex items-start justify-between">
												<div className="flex items-start gap-3 flex-1">
													<Checkbox
														checked={selectedItems.includes(item.id)}
														onCheckedChange={() => handleItemSelect(item.id)}
													/>
													
													<div className="flex-1 space-y-2">
														{/* Informações principais */}
														<div className="flex items-center gap-4">
															<div className="text-lg font-semibold text-blue-600">
																{item.result.workingDaysCount} dias úteis
															</div>
															<div className="text-sm text-muted-foreground flex items-center gap-1">
																<Calendar className="h-3 w-3" />
																{format(item.calculation.startDate, "dd/MM/yyyy", { locale: ptBR })} - {format(item.result.endDate, "dd/MM/yyyy", { locale: ptBR })}
															</div>
															<div className="text-sm text-muted-foreground flex items-center gap-1">
																<Clock className="h-3 w-3" />
																{format(item.timestamp, "dd/MM/yyyy HH:mm", { locale: ptBR })}
															</div>
														</div>

														{/* Detalhes */}
														<div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
															<div>
																<span className="text-muted-foreground">Total de dias:</span>
																<div className="font-medium">{item.result.totalDays || 'N/A'}</div>
															</div>
															<div>
																<span className="text-muted-foreground">Feriados:</span>
																<div className="font-medium">{item.result.holidaysInPeriod?.length || 0}</div>
															</div>
															<div>
																<span className="text-muted-foreground">Fins de semana:</span>
																<div className="font-medium">{item.result.weekendsCount || 'N/A'}</div>
															</div>
															<div>
																<span className="text-muted-foreground">Incluir início:</span>
																<div className="font-medium">{item.calculation.includeStartDate ? 'Sim' : 'Não'}</div>
															</div>
														</div>

														{/* Tags */}
														<div className="flex items-center gap-2 flex-wrap">
															{item.tags?.map(tag => (
																<Badge 
																	key={tag} 
																	variant="secondary" 
																	className="text-xs cursor-pointer"
																	onClick={() => handleRemoveTag(item.id, tag)}
																>
																	{tag}
																	<X className="h-3 w-3 ml-1" />
																</Badge>
															))}
															
															{editingTags === item.id ? (
																<div className="flex items-center gap-1">
																	<Input
																		size="sm"
																		placeholder="Nova tag"
																		value={newTag}
																		onChange={(e) => setNewTag(e.target.value)}
																		onKeyPress={(e) => {
																			if (e.key === 'Enter') {
																				handleAddTag(item.id);
																			}
																		}}
																		className="w-24 h-6"
																	/>
																	<Button
																		size="sm"
																		onClick={() => handleAddTag(item.id)}
																		className="h-6 px-2"
																	>
																		<Plus className="h-3 w-3" />
																	</Button>
																</div>
															) : (
																<Button
																	size="sm"
																	variant="ghost"
																	onClick={() => setEditingTags(item.id)}
																	className="h-6 px-2 text-xs"
																>
																	<Tag className="h-3 w-3 mr-1" />
																	Adicionar tag
																</Button>
															)}
														</div>
													</div>
												</div>

												{/* Ações */}
												<div className="flex items-center gap-1">
													<Button
														size="sm"
														variant="ghost"
														onClick={() => onSelectCalculation?.(item)}
														className="flex items-center gap-1"
													>
														<Eye className="h-3 w-3" />
													</Button>
													<Button
														size="sm"
														variant="ghost"
														onClick={() => onCalculationReuse?.(item)}
														className="flex items-center gap-1"
													>
														<Edit className="h-3 w-3" />
													</Button>
													<Button
														size="sm"
														variant="ghost"
														onClick={() => removeHistoryItem(item.id)}
														className="flex items-center gap-1 text-red-600 hover:text-red-700"
													>
														<Trash2 className="h-3 w-3" />
													</Button>
												</div>
											</div>
										</CardContent>
									</Card>
								))
							)}
						</div>
					</TabsContent>

					{/* Aba de Estatísticas */}
					<TabsContent value="stats" className="space-y-4">
						{stats ? (
							<div className="space-y-6">
								{/* Resumo geral */}
								<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
									<Card>
										<CardContent className="pt-6">
											<div className="text-2xl font-bold">{stats.totalCalculations}</div>
											<p className="text-xs text-muted-foreground">Total de cálculos</p>
										</CardContent>
									</Card>
									<Card>
										<CardContent className="pt-6">
											<div className="text-2xl font-bold text-blue-600">{stats.totalWorkingDays}</div>
											<p className="text-xs text-muted-foreground">Dias úteis totais</p>
										</CardContent>
									</Card>
									<Card>
										<CardContent className="pt-6">
											<div className="text-2xl font-bold text-green-600">{stats.averageWorkingDays}</div>
											<p className="text-xs text-muted-foreground">Média por projeto</p>
										</CardContent>
									</Card>
									<Card>
										<CardContent className="pt-6">
											<div className="text-2xl font-bold text-purple-600">{stats.mostUsedWorkingDays}</div>
											<p className="text-xs text-muted-foreground">Mais comum</p>
										</CardContent>
									</Card>
								</div>

								{/* Atividade recente */}
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<Card>
										<CardHeader>
											<CardTitle className="text-base">Atividade Recente</CardTitle>
										</CardHeader>
										<CardContent>
											<div className="space-y-3">
												<div className="flex justify-between">
													<span className="text-sm text-muted-foreground">Esta semana</span>
													<span className="font-medium">{stats.calculationsThisWeek} cálculos</span>
												</div>
												<div className="flex justify-between">
													<span className="text-sm text-muted-foreground">Este mês</span>
													<span className="font-medium">{stats.calculationsThisMonth} cálculos</span>
												</div>
												{stats.periodRange && (
													<div className="flex justify-between">
														<span className="text-sm text-muted-foreground">Período total</span>
														<span className="font-medium">
															{format(stats.periodRange.start, "dd/MM/yyyy", { locale: ptBR })} - {format(stats.periodRange.end, "dd/MM/yyyy", { locale: ptBR })}
														</span>
													</div>
												)}
											</div>
										</CardContent>
									</Card>

									<Card>
										<CardHeader>
											<CardTitle className="text-base">Distribuição por Tamanho</CardTitle>
										</CardHeader>
										<CardContent>
											<div className="space-y-3">
												<div className="flex justify-between">
													<span className="text-sm text-muted-foreground">Projetos pequenos (≤10 dias)</span>
													<span className="font-medium">{stats.projectSizeDistribution.small}</span>
												</div>
												<div className="flex justify-between">
													<span className="text-sm text-muted-foreground">Projetos médios (11-30 dias)</span>
													<span className="font-medium">{stats.projectSizeDistribution.medium}</span>
												</div>
												<div className="flex justify-between">
													<span className="text-sm text-muted-foreground">Projetos grandes (>30 dias)</span>
													<span className="font-medium">{stats.projectSizeDistribution.large}</span>
												</div>
											</div>
										</CardContent>
									</Card>
								</div>

								{/* Tags mais usadas */}
								{stats.topTags.length > 0 && (
									<Card>
										<CardHeader>
											<CardTitle className="text-base">Tags Mais Usadas</CardTitle>
										</CardHeader>
										<CardContent>
											<div className="flex flex-wrap gap-2">
												{stats.topTags.map(({ tag, count }) => (
													<Badge key={tag} variant="outline" className="flex items-center gap-1">
														{tag}
														<span className="text-xs text-muted-foreground">({count})</span>
													</Badge>
												))}
											</div>
										</CardContent>
									</Card>
								)}
							</div>
						) : (
							<Alert>
								<BarChart3 className="h-4 w-4" />
								<AlertTitle>Sem dados para estatísticas</AlertTitle>
								<AlertDescription>
									Realize alguns cálculos para ver as estatísticas aqui.
								</AlertDescription>
							</Alert>
						)}
					</TabsContent>

					{/* Aba de Configurações */}
					<TabsContent value="settings" className="space-y-4">
						{preferences ? (
							<div className="space-y-6">
								{/* Configurações padrão */}
								<Card>
									<CardHeader>
										<CardTitle className="text-base">Configurações Padrão</CardTitle>
										<CardDescription>
											Defina os valores padrão para novos cálculos
										</CardDescription>
									</CardHeader>
									<CardContent className="space-y-4">
										<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
											<div className="space-y-2">
												<Label>Incluir data inicial</Label>
												<Select
													value={preferences.defaultIncludeStartDate ? "true" : "false"}
													onValueChange={(value) => {
														// Implementar atualização de preferências
													}}
												>
													<SelectTrigger>
														<SelectValue />
													</SelectTrigger>
													<SelectContent>
														<SelectItem value="true">Sim</SelectItem>
														<SelectItem value="false">Não</SelectItem>
													</SelectContent>
												</Select>
											</div>

											<div className="space-y-2">
												<Label>Excluir feriados</Label>
												<Select
													value={preferences.defaultExcludeHolidays ? "true" : "false"}
													onValueChange={(value) => {
														// Implementar atualização de preferências
													}}
												>
													<SelectTrigger>
														<SelectValue />
													</SelectTrigger>
													<SelectContent>
														<SelectItem value="true">Sim</SelectItem>
														<SelectItem value="false">Não</SelectItem>
													</SelectContent>
												</Select>
											</div>

											<div className="space-y-2">
												<Label>Formato de exportação padrão</Label>
												<Select
													value={preferences.defaultExportFormat}
													onValueChange={(value) => {
														// Implementar atualização de preferências
													}}
												>
													<SelectTrigger>
														<SelectValue />
													</SelectTrigger>
													<SelectContent>
														<SelectItem value="csv">CSV</SelectItem>
														<SelectItem value="excel">Excel</SelectItem>
														<SelectItem value="pdf">PDF</SelectItem>
														<SelectItem value="json">JSON</SelectItem>
													</SelectContent>
												</Select>
											</div>

											<div className="space-y-2">
												<Label>Máximo de itens no histórico</Label>
												<Input
													type="number"
													value={preferences.maxHistoryItems}
													onChange={(e) => {
														// Implementar atualização de preferências
													}}
													min="10"
													max="1000"
												/>
											</div>
										</div>

										<div className="space-y-2">
											<Label>Salvar cálculos automaticamente</Label>
											<div className="flex items-center space-x-2">
												<Checkbox
													checked={preferences.autoSaveCalculations}
													onCheckedChange={(checked) => {
														// Implementar atualização de preferências
													}}
												/>
												<span className="text-sm">Salvar todos os cálculos no histórico automaticamente</span>
											</div>
										</div>
									</CardContent>
								</Card>

								{/* Configurações de notificações */}
								<Card>
									<CardHeader>
										<CardTitle className="text-base">Notificações</CardTitle>
										<CardDescription>
											Configure quais notificações você deseja receber
										</CardDescription>
									</CardHeader>
									<CardContent className="space-y-4">
										<div className="space-y-3">
											<div className="flex items-center space-x-2">
												<Checkbox
													checked={preferences.notifications.showSuccess}
													onCheckedChange={(checked) => {
														// Implementar atualização de preferências
													}}
												/>
												<span className="text-sm">Mostrar notificações de sucesso</span>
											</div>
											<div className="flex items-center space-x-2">
												<Checkbox
													checked={preferences.notifications.showErrors}
													onCheckedChange={(checked) => {
														// Implementar atualização de preferências
													}}
												/>
												<span className="text-sm">Mostrar notificações de erro</span>
											</div>
											<div className="flex items-center space-x-2">
												<Checkbox
													checked={preferences.notifications.showWarnings}
													onCheckedChange={(checked) => {
														// Implementar atualização de preferências
													}}
												/>
												<span className="text-sm">Mostrar avisos e alertas</span>
											</div>
										</div>
									</CardContent>
								</Card>

								{/* Ações de manutenção */}
								<Card>
									<CardHeader>
										<CardTitle className="text-base">Manutenção</CardTitle>
										<CardDescription>
											Ações para gerenciar seus dados
										</CardDescription>
									</CardHeader>
									<CardContent>
										<div className="flex flex-wrap gap-3">
											<Button
												variant="outline"
												onClick={exportHistory}
												className="flex items-center gap-2"
											>
												<Download className="h-4 w-4" />
												Fazer Backup
											</Button>

											<div>
												<input
													type="file"
													accept=".json"
													onChange={handleImportFile}
													className="hidden"
													id="restore-backup"
												/>
												<Button
													variant="outline"
													onClick={() => document.getElementById('restore-backup')?.click()}
													className="flex items-center gap-2"
												>
													<Upload className="h-4 w-4" />
													Restaurar Backup
												</Button>
											</div>

											<Dialog>
												<DialogTrigger asChild>
													<Button variant="destructive" className="flex items-center gap-2">
														<Trash2 className="h-4 w-4" />
														Limpar Tudo
													</Button>
												</DialogTrigger>
												<DialogContent>
													<DialogHeader>
														<DialogTitle>Confirmar Limpeza Completa</DialogTitle>
														<DialogDescription>
															Esta ação irá remover permanentemente todo o histórico,
															configurações e preferências. Esta ação não pode ser desfeita.
														</DialogDescription>
													</DialogHeader>
													<div className="flex justify-end gap-2">
														<Button variant="outline">Cancelar</Button>
														<Button
															variant="destructive"
															onClick={async () => {
																await clearHistory();
																// Também limpar preferências se necessário
															}}
														>
															Confirmar Limpeza
														</Button>
													</div>
												</DialogContent>
											</Dialog>
										</div>
									</CardContent>
								</Card>
							</div>
						) : (
							<Alert>
								<Settings className="h-4 w-4" />
								<AlertTitle>Carregando configurações</AlertTitle>
								<AlertDescription>
									Aguarde enquanto carregamos suas preferências...
								</AlertDescription>
							</Alert>
						)}
					</TabsContent>
				</Tabs>

				{/* Estados de erro */}
				{error && (
					<Alert variant="destructive" className="mt-4">
						<AlertTitle>Erro</AlertTitle>
						<AlertDescription>{error}</AlertDescription>
					</Alert>
				)}

				{/* Ações gerais do histórico */}
				<div className="flex items-center justify-between pt-4 border-t">
					<div className="flex gap-2">
						<Button
							variant="outline"
							onClick={exportHistory}
							className="flex items-center gap-2"
						>
							<Download className="h-4 w-4" />
							Exportar Backup
						</Button>

						<div>
							<input
								type="file"
								accept=".json"
								onChange={handleImportFile}
								className="hidden"
								id="import-backup-main"
							/>
							<Button
								variant="outline"
								onClick={() => document.getElementById('import-backup-main')?.click()}
								className="flex items-center gap-2"
							>
								<Upload className="h-4 w-4" />
								Importar Backup
							</Button>
						</div>
					</div>

					<Dialog>
						<DialogTrigger asChild>
							<Button variant="destructive" className="flex items-center gap-2">
								<Trash2 className="h-4 w-4" />
								Limpar Histórico
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Confirmar Limpeza</DialogTitle>
								<DialogDescription>
									Esta ação irá remover permanentemente todo o histórico de cálculos.
									Esta ação não pode ser desfeita.
								</DialogDescription>
							</DialogHeader>
							<div className="flex justify-end gap-2">
								<Button variant="outline">Cancelar</Button>
								<Button variant="destructive" onClick={clearHistory}>
									Confirmar Limpeza
								</Button>
							</div>
						</DialogContent>
					</Dialog>
				</div>
			</CardContent>
		</Card>
	);
}

interface IHoliday {
	date: string; // formato YYYY-MM-DD
	name: string;
	type: "nacional" | "estadual" | "municipal";
}

interface IHolidayService {
	getHolidaysForYear: (year: number) => IHoliday[];
	isHoliday: (date: Date) => boolean;
	getHolidayName: (date: Date) => string | null;
}

// Feriados nacionais fixos
const FIXED_HOLIDAYS: Omit<IHoliday, "date">[] = [
	{ name: "Confraternização Universal", type: "nacional" },
	{ name: "Tiradentes", type: "nacional" },
	{ name: "Dia do Trabalho", type: "nacional" },
	{ name: "Independência do Brasil", type: "nacional" },
	{ name: "Nossa Senhora Aparecida", type: "nacional" },
	{ name: "Finados", type: "nacional" },
	{ name: "Proclamação da República", type: "nacional" },
	{ name: "Natal", type: "nacional" },
];

// Feriados móveis (Páscoa e derivados)
function getEasterDate(year: number): Date {
	const a = year % 19;
	const b = Math.floor(year / 100);
	const c = year % 100;
	const d = Math.floor(b / 4);
	const e = b % 4;
	const f = Math.floor((b + 8) / 25);
	const g = Math.floor((b - f + 1) / 3);
	const h = (19 * a + b - d - g + 15) % 30;
	const i = Math.floor(c / 4);
	const k = c % 4;
	const l = (32 + 2 * e + 2 * i - h - k) % 7;
	const m = Math.floor((a + 11 * h + 22 * l) / 451);
	const month = Math.floor((h + l - 7 * m + 114) / 31);
	const day = ((h + l - 7 * m + 114) % 31) + 1;

	return new Date(year, month - 1, day);
}

function getMovableHolidays(year: number): IHoliday[] {
	const easter = getEasterDate(year);
	const carnival = new Date(easter);
	carnival.setDate(easter.getDate() - 47);

	const goodFriday = new Date(easter);
	goodFriday.setDate(easter.getDate() - 2);

	const corpusChristi = new Date(easter);
	corpusChristi.setDate(easter.getDate() + 60);

	return [
		{
			date: carnival.toISOString().split("T")[0],
			name: "Carnaval",
			type: "nacional",
		},
		{
			date: goodFriday.toISOString().split("T")[0],
			name: "Sexta-feira Santa",
			type: "nacional",
		},
		{
			date: easter.toISOString().split("T")[0],
			name: "Páscoa",
			type: "nacional",
		},
		{
			date: corpusChristi.toISOString().split("T")[0],
			name: "Corpus Christi",
			type: "nacional",
		},
	];
}

export class HolidayService implements IHolidayService {
	private holidaysCache: Map<number, IHoliday[]> = new Map();

	getHolidaysForYear(year: number): IHoliday[] {
		if (this.holidaysCache.has(year)) {
			return this.holidaysCache.get(year)!;
		}

		const holidays: IHoliday[] = [];

		// Adiciona feriados fixos
		FIXED_HOLIDAYS.forEach(holiday => {
			const month =
				holiday.name === "Confraternização Universal"
					? 0 // Janeiro
					: holiday.name === "Tiradentes"
					? 3 // Abril
					: holiday.name === "Dia do Trabalho"
					? 4 // Maio
					: holiday.name === "Independência do Brasil"
					? 8 // Setembro
					: holiday.name === "Nossa Senhora Aparecida"
					? 9 // Outubro
					: holiday.name === "Finados"
					? 10 // Novembro
					: holiday.name === "Proclamação da República"
					? 10 // Novembro
					: 11; // Dezembro (Natal)

			const day =
				holiday.name === "Confraternização Universal"
					? 1
					: holiday.name === "Tiradentes"
					? 21
					: holiday.name === "Dia do Trabalho"
					? 1
					: holiday.name === "Independência do Brasil"
					? 7
					: holiday.name === "Nossa Senhora Aparecida"
					? 12
					: holiday.name === "Finados"
					? 2
					: holiday.name === "Proclamação da República"
					? 15
					: 25; // Natal

			holidays.push({
				date: `${year}-${String(month + 1).padStart(2, "0")}-${String(day).padStart(2, "0")}`,
				name: holiday.name,
				type: holiday.type,
			});
		});

		// Adiciona feriados móveis
		holidays.push(...getMovableHolidays(year));

		this.holidaysCache.set(year, holidays);
		return holidays;
	}

	isHoliday(date: Date): boolean {
		const year = date.getFullYear();
		const holidays = this.getHolidaysForYear(year);
		const dateString = date.toISOString().split("T")[0];

		return holidays.some(holiday => holiday.date === dateString);
	}

	getHolidayName(date: Date): string | null {
		const year = date.getFullYear();
		const holidays = this.getHolidaysForYear(year);
		const dateString = date.toISOString().split("T")[0];

		const holiday = holidays.find(h => h.date === dateString);
		return holiday ? holiday.name : null;
	}
}

export const holidayService = new HolidayService();

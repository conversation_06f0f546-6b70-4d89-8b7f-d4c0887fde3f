import { useState, useCallback } from "react";
import { 
	IExportHook,
	ICalculationHistory,
	IProjectTemplate,
	IExportOptions,
	IReportData
} from "@/types";
import { exportService } from "@/services/exportService";
import { useNotifications } from "./useNotifications";

export function useExport(): IExportHook {
	const [isExporting, setIsExporting] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [lastExportData, setLastExportData] = useState<IReportData | null>(null);
	
	const { addNotification } = useNotifications();

	// Exportar para CSV
	const exportToCSV = useCallback(async (
		calculations: ICalculationHistory[],
		options: IExportOptions = {}
	) => {
		setIsExporting(true);
		setError(null);

		try {
			const reportData = exportService.prepareReportData(calculations);
			const result = await exportService.exportToCSV(reportData, options);
			
			setLastExportData(reportData);
			
			addNotification({
				type: "success",
				title: "Exportação concluída",
				message: `Dados exportados para CSV com sucesso`,
				duration: 3000,
			});

			return result;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao exportar para CSV";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro na exportação",
				message: errorMessage,
				duration: 5000,
			});
			throw err;
		} finally {
			setIsExporting(false);
		}
	}, [addNotification]);

	// Exportar para Excel
	const exportToExcel = useCallback(async (
		calculations: ICalculationHistory[],
		options: IExportOptions = {}
	) => {
		setIsExporting(true);
		setError(null);

		try {
			const reportData = exportService.prepareReportData(calculations);
			const result = await exportService.exportToExcel(reportData, options);
			
			setLastExportData(reportData);
			
			addNotification({
				type: "success",
				title: "Exportação concluída",
				message: `Dados exportados para Excel com sucesso`,
				duration: 3000,
			});

			return result;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao exportar para Excel";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro na exportação",
				message: errorMessage,
				duration: 5000,
			});
			throw err;
		} finally {
			setIsExporting(false);
		}
	}, [addNotification]);

	// Exportar para PDF
	const exportToPDF = useCallback(async (
		calculations: ICalculationHistory[],
		options: IExportOptions = {}
	) => {
		setIsExporting(true);
		setError(null);

		try {
			const reportData = exportService.prepareReportData(calculations);
			const result = await exportService.exportToPDF(reportData, options);
			
			setLastExportData(reportData);
			
			addNotification({
				type: "success",
				title: "Relatório gerado",
				message: `Relatório PDF aberto para impressão`,
				duration: 3000,
			});

			return result;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao gerar PDF";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro na geração do PDF",
				message: errorMessage,
				duration: 5000,
			});
			throw err;
		} finally {
			setIsExporting(false);
		}
	}, [addNotification]);

	// Exportar para iCal
	const exportToICal = useCallback(async (
		calculations: ICalculationHistory[],
		options: IExportOptions = {}
	) => {
		setIsExporting(true);
		setError(null);

		try {
			const reportData = exportService.prepareReportData(calculations);
			const result = await exportService.exportToICal(reportData, options);
			
			setLastExportData(reportData);
			
			addNotification({
				type: "success",
				title: "Calendário exportado",
				message: `Arquivo iCal gerado com sucesso`,
				duration: 3000,
			});

			return result;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao exportar calendário";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro na exportação do calendário",
				message: errorMessage,
				duration: 5000,
			});
			throw err;
		} finally {
			setIsExporting(false);
		}
	}, [addNotification]);

	// Exportar para JSON
	const exportToJSON = useCallback(async (
		calculations: ICalculationHistory[],
		options: IExportOptions = {}
	) => {
		setIsExporting(true);
		setError(null);

		try {
			const reportData = exportService.prepareReportData(calculations);
			const result = await exportService.exportToJSON(reportData, options);
			
			setLastExportData(reportData);
			
			addNotification({
				type: "success",
				title: "Dados exportados",
				message: `Dados exportados para JSON com sucesso`,
				duration: 3000,
			});

			return result;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao exportar para JSON";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro na exportação",
				message: errorMessage,
				duration: 5000,
			});
			throw err;
		} finally {
			setIsExporting(false);
		}
	}, [addNotification]);

	// Gerar relatório detalhado
	const generateDetailedReport = useCallback(async (
		calculations: ICalculationHistory[],
		templates?: IProjectTemplate[]
	) => {
		setIsExporting(true);
		setError(null);

		try {
			const reportData = exportService.prepareReportData(calculations, templates);
			const result = exportService.generateDetailedReport(reportData);
			
			setLastExportData(reportData);
			
			addNotification({
				type: "success",
				title: "Relatório gerado",
				message: `Relatório detalhado criado com sucesso`,
				duration: 3000,
			});

			return result;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao gerar relatório";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro na geração do relatório",
				message: errorMessage,
				duration: 5000,
			});
			throw err;
		} finally {
			setIsExporting(false);
		}
	}, [addNotification]);

	// Exportar múltiplos formatos
	const exportMultipleFormats = useCallback(async (
		calculations: ICalculationHistory[],
		formats: Array<"csv" | "excel" | "pdf" | "ical" | "json">,
		options: IExportOptions = {}
	) => {
		setIsExporting(true);
		setError(null);

		try {
			const results: Record<string, string> = {};
			
			for (const format of formats) {
				switch (format) {
					case "csv":
						results.csv = await exportService.exportToCSV(
							exportService.prepareReportData(calculations), 
							{ ...options, filename: `relatorio-${Date.now()}.csv` }
						);
						break;
					case "excel":
						results.excel = await exportService.exportToExcel(
							exportService.prepareReportData(calculations), 
							{ ...options, filename: `relatorio-${Date.now()}.xlsx` }
						);
						break;
					case "pdf":
						results.pdf = await exportService.exportToPDF(
							exportService.prepareReportData(calculations), 
							{ ...options, filename: `relatorio-${Date.now()}.pdf` }
						);
						break;
					case "ical":
						results.ical = await exportService.exportToICal(
							exportService.prepareReportData(calculations), 
							{ ...options, filename: `calendario-${Date.now()}.ics` }
						);
						break;
					case "json":
						results.json = await exportService.exportToJSON(
							exportService.prepareReportData(calculations), 
							{ ...options, filename: `dados-${Date.now()}.json` }
						);
						break;
				}
			}

			addNotification({
				type: "success",
				title: "Exportação múltipla concluída",
				message: `${formats.length} formatos exportados com sucesso`,
				duration: 4000,
			});

			return results;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro na exportação múltipla";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro na exportação múltipla",
				message: errorMessage,
				duration: 5000,
			});
			throw err;
		} finally {
			setIsExporting(false);
		}
	}, [addNotification]);

	// Limpar estado
	const clearExportState = useCallback(() => {
		setError(null);
		setLastExportData(null);
	}, []);

	return {
		isExporting,
		error,
		lastExportData,
		exportToCSV,
		exportToExcel,
		exportToPDF,
		exportToICal,
		exportToJSON,
		generateDetailedReport,
		exportMultipleFormats,
		clearExportState,
	};
}

// Hook especializado para relatórios
export function useReports() {
	const { 
		generateDetailedReport, 
		exportToPDF, 
		isExporting, 
		error 
	} = useExport();

	// Gerar relatório de produtividade
	const generateProductivityReport = useCallback(async (calculations: ICalculationHistory[]) => {
		if (calculations.length === 0) return null;

		const totalWorkingDays = calculations.reduce((sum, calc) => sum + calc.result.workingDaysCount, 0);
		const averageWorkingDays = totalWorkingDays / calculations.length;
		
		// Análise por período
		const monthlyData = calculations.reduce((acc, calc) => {
			const month = calc.calculation.startDate.toISOString().substring(0, 7); // YYYY-MM
			if (!acc[month]) {
				acc[month] = { count: 0, totalDays: 0 };
			}
			acc[month].count++;
			acc[month].totalDays += calc.result.workingDaysCount;
			return acc;
		}, {} as Record<string, { count: number; totalDays: number }>);

		return {
			summary: {
				totalProjects: calculations.length,
				totalWorkingDays,
				averageWorkingDays: Math.round(averageWorkingDays),
				averageProjectDuration: Math.round(averageWorkingDays),
			},
			monthlyBreakdown: Object.entries(monthlyData).map(([month, data]) => ({
				month,
				projectCount: data.count,
				totalWorkingDays: data.totalDays,
				averageProjectSize: Math.round(data.totalDays / data.count),
			})),
			trends: {
				mostProductiveMonth: Object.entries(monthlyData).reduce((max, [month, data]) => 
					data.totalDays > max.totalDays ? { month, ...data } : max
				, { month: "", count: 0, totalDays: 0 }),
				projectSizeDistribution: {
					small: calculations.filter(c => c.result.workingDaysCount <= 10).length,
					medium: calculations.filter(c => c.result.workingDaysCount > 10 && c.result.workingDaysCount <= 30).length,
					large: calculations.filter(c => c.result.workingDaysCount > 30).length,
				}
			}
		};
	}, []);

	return {
		generateDetailedReport,
		generateProductivityReport,
		exportToPDF,
		isExporting,
		error,
	};
}

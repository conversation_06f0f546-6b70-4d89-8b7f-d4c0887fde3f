import { useState, useEffect, useCallback } from "react";
import { 
	IHistoryHook,
	ICalculationHistory,
	IWorkingDaysCalculation,
	IWorkingDaysResult,
	IUserPreferences,
	IHistoryFilter,
	IHistoryStats
} from "@/types";
import { historyService } from "@/services/historyService";
import { useNotifications } from "./useNotifications";

export function useHistory(): IHistoryHook {
	const [history, setHistory] = useState<ICalculationHistory[]>([]);
	const [filteredHistory, setFilteredHistory] = useState<ICalculationHistory[]>([]);
	const [stats, setStats] = useState<IHistoryStats | null>(null);
	const [preferences, setPreferences] = useState<IUserPreferences | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [currentFilter, setCurrentFilter] = useState<IHistoryFilter>({});

	const { addNotification } = useNotifications();

	// Carregar histórico inicial
	useEffect(() => {
		loadHistory();
		loadPreferences();
		loadStats();
	}, []);

	// Aplicar filtro quando histórico ou filtro mudar
	useEffect(() => {
		applyFilter(currentFilter);
	}, [history, currentFilter]);

	// Carregar histórico
	const loadHistory = useCallback(async () => {
		setIsLoading(true);
		setError(null);

		try {
			const historyData = await historyService.getHistory();
			setHistory(historyData);
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao carregar histórico";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro ao carregar histórico",
				message: errorMessage,
				duration: 5000,
			});
		} finally {
			setIsLoading(false);
		}
	}, [addNotification]);

	// Carregar estatísticas
	const loadStats = useCallback(async () => {
		try {
			const statsData = await historyService.getHistoryStats();
			setStats(statsData);
		} catch (err) {
			console.error("Erro ao carregar estatísticas:", err);
		}
	}, []);

	// Carregar preferências
	const loadPreferences = useCallback(async () => {
		try {
			const preferencesData = await historyService.getUserPreferences();
			setPreferences(preferencesData);
		} catch (err) {
			console.error("Erro ao carregar preferências:", err);
		}
	}, []);

	// Adicionar cálculo ao histórico
	const addCalculation = useCallback(async (
		calculation: IWorkingDaysCalculation,
		result: IWorkingDaysResult
	) => {
		setError(null);

		try {
			const historyItem = await historyService.addCalculation(calculation, result);
			
			// Atualizar estado local
			setHistory(prev => [historyItem, ...prev]);
			
			// Recarregar estatísticas
			await loadStats();
			
			addNotification({
				type: "success",
				title: "Cálculo salvo",
				message: "Cálculo adicionado ao histórico",
				duration: 2000,
			});

			return historyItem;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao salvar cálculo";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro ao salvar",
				message: errorMessage,
				duration: 5000,
			});
			throw err;
		}
	}, [addNotification, loadStats]);

	// Remover item do histórico
	const removeHistoryItem = useCallback(async (id: string) => {
		setError(null);

		try {
			const success = await historyService.removeHistoryItem(id);
			
			if (success) {
				setHistory(prev => prev.filter(item => item.id !== id));
				await loadStats();
				
				addNotification({
					type: "success",
					title: "Item removido",
					message: "Item removido do histórico",
					duration: 2000,
				});
			}

			return success;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao remover item";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro ao remover",
				message: errorMessage,
				duration: 5000,
			});
			return false;
		}
	}, [addNotification, loadStats]);

	// Limpar histórico
	const clearHistory = useCallback(async () => {
		setError(null);

		try {
			await historyService.clearHistory();
			setHistory([]);
			setFilteredHistory([]);
			await loadStats();
			
			addNotification({
				type: "success",
				title: "Histórico limpo",
				message: "Todo o histórico foi removido",
				duration: 3000,
			});
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao limpar histórico";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro ao limpar histórico",
				message: errorMessage,
				duration: 5000,
			});
		}
	}, [addNotification, loadStats]);

	// Atualizar tags de um item
	const updateItemTags = useCallback(async (id: string, tags: string[]) => {
		setError(null);

		try {
			const success = await historyService.updateHistoryItemTags(id, tags);
			
			if (success) {
				setHistory(prev => prev.map(item => 
					item.id === id ? { ...item, tags } : item
				));
				
				addNotification({
					type: "success",
					title: "Tags atualizadas",
					message: "Tags do item foram atualizadas",
					duration: 2000,
				});
			}

			return success;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao atualizar tags";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro ao atualizar tags",
				message: errorMessage,
				duration: 5000,
			});
			return false;
		}
	}, [addNotification]);

	// Aplicar filtro
	const applyFilter = useCallback(async (filter: IHistoryFilter) => {
		setCurrentFilter(filter);
		
		try {
			const filtered = await historyService.getFilteredHistory(filter);
			setFilteredHistory(filtered);
		} catch (err) {
			console.error("Erro ao aplicar filtro:", err);
			setFilteredHistory(history); // Fallback para histórico completo
		}
	}, [history]);

	// Limpar filtro
	const clearFilter = useCallback(() => {
		setCurrentFilter({});
		setFilteredHistory(history);
	}, [history]);

	// Salvar preferências
	const savePreferences = useCallback(async (newPreferences: IUserPreferences) => {
		setError(null);

		try {
			await historyService.saveUserPreferences(newPreferences);
			setPreferences(newPreferences);
			
			addNotification({
				type: "success",
				title: "Preferências salvas",
				message: "Suas preferências foram atualizadas",
				duration: 2000,
			});
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao salvar preferências";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro ao salvar preferências",
				message: errorMessage,
				duration: 5000,
			});
		}
	}, [addNotification]);

	// Exportar histórico
	const exportHistory = useCallback(async () => {
		setError(null);

		try {
			const backupData = await historyService.exportHistory();
			
			// Fazer download do backup
			const blob = new Blob([backupData], { type: "application/json" });
			const url = URL.createObjectURL(blob);
			const link = document.createElement("a");
			link.href = url;
			link.download = `backup-historico-${new Date().toISOString().split('T')[0]}.json`;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
			URL.revokeObjectURL(url);
			
			addNotification({
				type: "success",
				title: "Backup criado",
				message: "Histórico exportado com sucesso",
				duration: 3000,
			});

			return backupData;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao exportar histórico";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro ao exportar",
				message: errorMessage,
				duration: 5000,
			});
			throw err;
		}
	}, [addNotification]);

	// Importar histórico
	const importHistory = useCallback(async (backupData: string) => {
		setError(null);
		setIsLoading(true);

		try {
			const success = await historyService.importHistory(backupData);
			
			if (success) {
				await loadHistory();
				await loadPreferences();
				await loadStats();
				
				addNotification({
					type: "success",
					title: "Backup restaurado",
					message: "Histórico importado com sucesso",
					duration: 3000,
				});
			} else {
				throw new Error("Falha ao importar dados");
			}

			return success;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao importar histórico";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro ao importar",
				message: errorMessage,
				duration: 5000,
			});
			return false;
		} finally {
			setIsLoading(false);
		}
	}, [addNotification, loadHistory, loadPreferences, loadStats]);

	// Obter item específico
	const getHistoryItem = useCallback(async (id: string) => {
		try {
			return await historyService.getHistoryItem(id);
		} catch (err) {
			console.error("Erro ao obter item do histórico:", err);
			return null;
		}
	}, []);

	return {
		history,
		filteredHistory,
		stats,
		preferences,
		isLoading,
		error,
		currentFilter,
		addCalculation,
		removeHistoryItem,
		clearHistory,
		updateItemTags,
		applyFilter,
		clearFilter,
		savePreferences,
		exportHistory,
		importHistory,
		getHistoryItem,
		refreshHistory: loadHistory,
		refreshStats: loadStats,
	};
}

// Hook especializado para estatísticas
export function useHistoryStats() {
	const { stats, refreshStats } = useHistory();
	
	return {
		stats,
		refreshStats,
	};
}

// Hook especializado para preferências
export function useUserPreferences() {
	const { preferences, savePreferences, isLoading, error } = useHistory();
	
	return {
		preferences,
		savePreferences,
		isLoading,
		error,
	};
}

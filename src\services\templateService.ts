import { ITemplateService, IProjectTemplate, IProjectPhase } from "@/types";

class TemplateService implements ITemplateService {
	private defaultTemplates: IProjectTemplate[] = [
		{
			id: "web-development",
			name: "Desenvolvimento Web",
			description: "Template para projetos de desenvolvimento web completos",
			category: "desenvolvimento",
			defaultWorkingDays: 30,
			workingDaysPattern: [1, 2, 3, 4, 5], // Segunda a Sexta
			excludeHolidays: true,
			estimatedBufferDays: 5,
			phases: [
				{
					id: "planning",
					name: "Planejamento",
					description: "Análise de requisitos e planejamento do projeto",
					estimatedDays: 5,
					dependencies: [],
					isOptional: false,
				},
				{
					id: "design",
					name: "Design e Prototipagem",
					description: "Criação de wireframes, mockups e protótipos",
					estimatedDays: 8,
					dependencies: ["planning"],
					isOptional: false,
				},
				{
					id: "frontend",
					name: "Desenvolvimento Frontend",
					description: "Implementação da interface do usuário",
					estimatedDays: 12,
					dependencies: ["design"],
					isOptional: false,
				},
				{
					id: "backend",
					name: "Desenvolvimento Backend",
					description: "Implementação da lógica de negócio e APIs",
					estimatedDays: 10,
					dependencies: ["planning"],
					isOptional: false,
				},
				{
					id: "integration",
					name: "Integração",
					description: "Integração entre frontend e backend",
					estimatedDays: 3,
					dependencies: ["frontend", "backend"],
					isOptional: false,
				},
				{
					id: "testing",
					name: "Testes",
					description: "Testes unitários, integração e end-to-end",
					estimatedDays: 5,
					dependencies: ["integration"],
					isOptional: false,
				},
				{
					id: "deployment",
					name: "Deploy e Configuração",
					description: "Deploy em produção e configurações finais",
					estimatedDays: 2,
					dependencies: ["testing"],
					isOptional: false,
				},
			],
			createdAt: new Date("2024-01-01"),
			updatedAt: new Date("2024-01-01"),
		},
		{
			id: "mobile-app",
			name: "Aplicativo Mobile",
			description: "Template para desenvolvimento de aplicativos móveis",
			category: "desenvolvimento",
			defaultWorkingDays: 45,
			workingDaysPattern: [1, 2, 3, 4, 5],
			excludeHolidays: true,
			estimatedBufferDays: 8,
			phases: [
				{
					id: "research",
					name: "Pesquisa e Análise",
					description: "Pesquisa de mercado e análise de concorrentes",
					estimatedDays: 5,
					dependencies: [],
					isOptional: false,
				},
				{
					id: "ux-design",
					name: "UX Design",
					description: "Design da experiência do usuário",
					estimatedDays: 8,
					dependencies: ["research"],
					isOptional: false,
				},
				{
					id: "ui-design",
					name: "UI Design",
					description: "Design da interface do usuário",
					estimatedDays: 6,
					dependencies: ["ux-design"],
					isOptional: false,
				},
				{
					id: "development",
					name: "Desenvolvimento",
					description: "Desenvolvimento do aplicativo",
					estimatedDays: 20,
					dependencies: ["ui-design"],
					isOptional: false,
				},
				{
					id: "testing",
					name: "Testes",
					description: "Testes em dispositivos e plataformas",
					estimatedDays: 6,
					dependencies: ["development"],
					isOptional: false,
				},
				{
					id: "store-submission",
					name: "Submissão às Lojas",
					description: "Preparação e submissão para App Store e Google Play",
					estimatedDays: 3,
					dependencies: ["testing"],
					isOptional: false,
				},
			],
			createdAt: new Date("2024-01-01"),
			updatedAt: new Date("2024-01-01"),
		},
		{
			id: "marketing-campaign",
			name: "Campanha de Marketing",
			description: "Template para campanhas de marketing digital",
			category: "marketing",
			defaultWorkingDays: 20,
			workingDaysPattern: [1, 2, 3, 4, 5],
			excludeHolidays: true,
			estimatedBufferDays: 3,
			phases: [
				{
					id: "strategy",
					name: "Estratégia",
					description: "Definição de estratégia e objetivos",
					estimatedDays: 3,
					dependencies: [],
					isOptional: false,
				},
				{
					id: "content-creation",
					name: "Criação de Conteúdo",
					description: "Produção de materiais e conteúdos",
					estimatedDays: 8,
					dependencies: ["strategy"],
					isOptional: false,
				},
				{
					id: "campaign-setup",
					name: "Configuração da Campanha",
					description: "Configuração de anúncios e plataformas",
					estimatedDays: 2,
					dependencies: ["content-creation"],
					isOptional: false,
				},
				{
					id: "launch",
					name: "Lançamento",
					description: "Lançamento e monitoramento inicial",
					estimatedDays: 1,
					dependencies: ["campaign-setup"],
					isOptional: false,
				},
				{
					id: "optimization",
					name: "Otimização",
					description: "Análise e otimização da campanha",
					estimatedDays: 6,
					dependencies: ["launch"],
					isOptional: false,
				},
			],
			createdAt: new Date("2024-01-01"),
			updatedAt: new Date("2024-01-01"),
		},
		{
			id: "design-project",
			name: "Projeto de Design",
			description: "Template para projetos de design gráfico e branding",
			category: "design",
			defaultWorkingDays: 15,
			workingDaysPattern: [1, 2, 3, 4, 5],
			excludeHolidays: true,
			estimatedBufferDays: 2,
			phases: [
				{
					id: "briefing",
					name: "Briefing",
					description: "Coleta de requisitos e briefing com cliente",
					estimatedDays: 2,
					dependencies: [],
					isOptional: false,
				},
				{
					id: "research",
					name: "Pesquisa",
					description: "Pesquisa de referências e tendências",
					estimatedDays: 2,
					dependencies: ["briefing"],
					isOptional: false,
				},
				{
					id: "concepts",
					name: "Conceitos",
					description: "Desenvolvimento de conceitos iniciais",
					estimatedDays: 4,
					dependencies: ["research"],
					isOptional: false,
				},
				{
					id: "refinement",
					name: "Refinamento",
					description: "Refinamento e ajustes dos conceitos",
					estimatedDays: 4,
					dependencies: ["concepts"],
					isOptional: false,
				},
				{
					id: "finalization",
					name: "Finalização",
					description: "Finalização e entrega dos arquivos",
					estimatedDays: 3,
					dependencies: ["refinement"],
					isOptional: false,
				},
			],
			createdAt: new Date("2024-01-01"),
			updatedAt: new Date("2024-01-01"),
		},
		{
			id: "consulting-project",
			name: "Projeto de Consultoria",
			description: "Template para projetos de consultoria empresarial",
			category: "consultoria",
			defaultWorkingDays: 25,
			workingDaysPattern: [1, 2, 3, 4, 5],
			excludeHolidays: true,
			estimatedBufferDays: 4,
			phases: [
				{
					id: "diagnosis",
					name: "Diagnóstico",
					description: "Análise da situação atual da empresa",
					estimatedDays: 6,
					dependencies: [],
					isOptional: false,
				},
				{
					id: "analysis",
					name: "Análise",
					description: "Análise detalhada dos problemas identificados",
					estimatedDays: 5,
					dependencies: ["diagnosis"],
					isOptional: false,
				},
				{
					id: "recommendations",
					name: "Recomendações",
					description: "Desenvolvimento de recomendações e soluções",
					estimatedDays: 6,
					dependencies: ["analysis"],
					isOptional: false,
				},
				{
					id: "implementation-plan",
					name: "Plano de Implementação",
					description: "Criação do plano de implementação das soluções",
					estimatedDays: 4,
					dependencies: ["recommendations"],
					isOptional: false,
				},
				{
					id: "presentation",
					name: "Apresentação",
					description: "Apresentação dos resultados e plano de ação",
					estimatedDays: 2,
					dependencies: ["implementation-plan"],
					isOptional: false,
				},
				{
					id: "follow-up",
					name: "Acompanhamento",
					description: "Acompanhamento da implementação",
					estimatedDays: 2,
					dependencies: ["presentation"],
					isOptional: true,
				},
			],
			createdAt: new Date("2024-01-01"),
			updatedAt: new Date("2024-01-01"),
		},
	];

	private customTemplates: IProjectTemplate[] = [];

	getTemplates(): IProjectTemplate[] {
		return [...this.defaultTemplates, ...this.customTemplates];
	}

	getTemplateById(id: string): IProjectTemplate | null {
		const allTemplates = this.getTemplates();
		return allTemplates.find(template => template.id === id) || null;
	}

	createTemplate(template: Omit<IProjectTemplate, "id" | "createdAt" | "updatedAt">): IProjectTemplate {
		const newTemplate: IProjectTemplate = {
			...template,
			id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		this.customTemplates.push(newTemplate);
		return newTemplate;
	}

	updateTemplate(id: string, updates: Partial<IProjectTemplate>): IProjectTemplate | null {
		const templateIndex = this.customTemplates.findIndex(t => t.id === id);
		
		if (templateIndex === -1) {
			// Não é possível atualizar templates padrão
			return null;
		}

		this.customTemplates[templateIndex] = {
			...this.customTemplates[templateIndex],
			...updates,
			updatedAt: new Date(),
		};

		return this.customTemplates[templateIndex];
	}

	deleteTemplate(id: string): boolean {
		const templateIndex = this.customTemplates.findIndex(t => t.id === id);
		
		if (templateIndex === -1) {
			// Não é possível deletar templates padrão
			return false;
		}

		this.customTemplates.splice(templateIndex, 1);
		return true;
	}

	getDefaultTemplates(): IProjectTemplate[] {
		return [...this.defaultTemplates];
	}

	// Métodos utilitários

	/**
	 * Calcula o total de dias estimados de um template
	 */
	calculateTotalDays(template: IProjectTemplate): number {
		if (!template.phases || template.phases.length === 0) {
			return template.defaultWorkingDays;
		}

		const requiredPhases = template.phases.filter(phase => !phase.isOptional);
		return requiredPhases.reduce((total, phase) => total + phase.estimatedDays, 0);
	}

	/**
	 * Valida as dependências de um template
	 */
	validateDependencies(template: IProjectTemplate): { isValid: boolean; errors: string[] } {
		const errors: string[] = [];
		
		if (!template.phases) {
			return { isValid: true, errors: [] };
		}

		const phaseIds = template.phases.map(p => p.id);

		template.phases.forEach(phase => {
			phase.dependencies.forEach(depId => {
				if (!phaseIds.includes(depId)) {
					errors.push(`Fase "${phase.name}" tem dependência inválida: "${depId}"`);
				}
			});
		});

		// Verificar dependências circulares
		const visited = new Set<string>();
		const recursionStack = new Set<string>();

		const hasCycle = (phaseId: string): boolean => {
			if (recursionStack.has(phaseId)) {
				return true;
			}
			if (visited.has(phaseId)) {
				return false;
			}

			visited.add(phaseId);
			recursionStack.add(phaseId);

			const phase = template.phases!.find(p => p.id === phaseId);
			if (phase) {
				for (const depId of phase.dependencies) {
					if (hasCycle(depId)) {
						return true;
					}
				}
			}

			recursionStack.delete(phaseId);
			return false;
		};

		for (const phase of template.phases) {
			if (hasCycle(phase.id)) {
				errors.push(`Dependência circular detectada envolvendo a fase "${phase.name}"`);
				break;
			}
		}

		return { isValid: errors.length === 0, errors };
	}

	/**
	 * Ordena as fases de um template respeitando as dependências
	 */
	sortPhasesByDependencies(template: IProjectTemplate): IProjectPhase[] {
		if (!template.phases) {
			return [];
		}

		const sorted: IProjectPhase[] = [];
		const visited = new Set<string>();
		const phases = new Map(template.phases.map(p => [p.id, p]));

		const visit = (phaseId: string) => {
			if (visited.has(phaseId)) {
				return;
			}

			const phase = phases.get(phaseId);
			if (!phase) {
				return;
			}

			// Visitar dependências primeiro
			phase.dependencies.forEach(depId => {
				visit(depId);
			});

			visited.add(phaseId);
			sorted.push(phase);
		};

		template.phases.forEach(phase => {
			visit(phase.id);
		});

		return sorted;
	}

	/**
	 * Clona um template para customização
	 */
	cloneTemplate(templateId: string, newName: string): IProjectTemplate | null {
		const original = this.getTemplateById(templateId);
		if (!original) {
			return null;
		}

		return this.createTemplate({
			...original,
			name: newName,
			description: `Baseado em: ${original.name}`,
		});
	}
}

export const templateService = new TemplateService();

import { useState, useCallback, useEffect } from "react";
import { INotification } from "@/types";

interface INotificationHook {
	notifications: INotification[];
	addNotification: (notification: Omit<INotification, "id">) => string;
	removeNotification: (id: string) => void;
	clearAll: () => void;
}

export function useNotifications(): INotificationHook {
	const [notifications, setNotifications] = useState<INotification[]>([]);

	// Adicionar notificação
	const addNotification = useCallback((notification: Omit<INotification, "id">) => {
		const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
		const newNotification: INotification = {
			id,
			duration: 4000, // Duração padrão
			...notification,
		};

		setNotifications(prev => [...prev, newNotification]);

		// Auto-remover após duração especificada
		if (newNotification.duration && newNotification.duration > 0) {
			setTimeout(() => {
				removeNotification(id);
			}, newNotification.duration);
		}

		return id;
	}, []);

	// Remover notificação
	const removeNotification = useCallback((id: string) => {
		setNotifications(prev => prev.filter(notification => notification.id !== id));
	}, []);

	// Limpar todas as notificações
	const clearAll = useCallback(() => {
		setNotifications([]);
	}, []);

	return {
		notifications,
		addNotification,
		removeNotification,
		clearAll,
	};
}

// Hook para notificações específicas de validação
export function useValidationNotifications() {
	const { addNotification } = useNotifications();

	const notifyValidationError = useCallback((field: string, message: string) => {
		addNotification({
			type: "error",
			title: "Erro de Validação",
			message: `${field}: ${message}`,
			duration: 4000,
		});
	}, [addNotification]);

	const notifyValidationWarning = useCallback((field: string, message: string) => {
		addNotification({
			type: "warning",
			title: "Atenção",
			message: `${field}: ${message}`,
			duration: 3000,
		});
	}, [addNotification]);

	const notifySuccess = useCallback((message: string) => {
		addNotification({
			type: "success",
			title: "Sucesso",
			message,
			duration: 3000,
		});
	}, [addNotification]);

	return {
		notifyValidationError,
		notifyValidationWarning,
		notifySuccess,
	};
}

// Hook para notificações de sistema
export function useSystemNotifications() {
	const { addNotification } = useNotifications();

	const notifyDataSaved = useCallback((item: string) => {
		addNotification({
			type: "success",
			title: "Dados Salvos",
			message: `${item} foi salvo com sucesso`,
			duration: 2000,
		});
	}, [addNotification]);

	const notifyDataLoaded = useCallback((item: string) => {
		addNotification({
			type: "info",
			title: "Dados Carregados",
			message: `${item} foi carregado`,
			duration: 2000,
		});
	}, [addNotification]);

	const notifyError = useCallback((operation: string, error?: string) => {
		addNotification({
			type: "error",
			title: "Erro na Operação",
			message: error || `Erro ao executar: ${operation}`,
			duration: 5000,
		});
	}, [addNotification]);

	const notifyExportComplete = useCallback((format: string, filename?: string) => {
		addNotification({
			type: "success",
			title: "Exportação Concluída",
			message: filename 
				? `Arquivo ${filename} exportado em formato ${format.toUpperCase()}`
				: `Dados exportados em formato ${format.toUpperCase()}`,
			duration: 4000,
		});
	}, [addNotification]);

	const notifyImportComplete = useCallback((itemCount: number, type: string) => {
		addNotification({
			type: "success",
			title: "Importação Concluída",
			message: `${itemCount} ${type}(s) importado(s) com sucesso`,
			duration: 3000,
		});
	}, [addNotification]);

	return {
		notifyDataSaved,
		notifyDataLoaded,
		notifyError,
		notifyExportComplete,
		notifyImportComplete,
	};
}

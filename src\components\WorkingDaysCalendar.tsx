"use client";

import React, { useState, useMemo } from "react";
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isToday, isWeekend, addMonths, subMonths } from "date-fns";
import { ptBR } from "date-fns/locale";
import { ChevronLeft, ChevronRight, Calendar, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { useHolidayManager } from "@/hooks/useHolidayManager";
import { IWorkingDaysCalculation, IWorkingDaysResult } from "@/types";

interface IWorkingDaysCalendarProps {
	calculation?: IWorkingDaysCalculation;
	result?: IWorkingDaysResult;
	onDateSelect?: (date: Date) => void;
	highlightPeriod?: boolean;
	showLegend?: boolean;
	compact?: boolean;
}

export function WorkingDaysCalendar({
	calculation,
	result,
	onDateSelect,
	highlightPeriod = true,
	showLegend = true,
	compact = false
}: IWorkingDaysCalendarProps) {
	const [currentMonth, setCurrentMonth] = useState(new Date());
	const { isHoliday } = useHolidayManager();

	// Calcular dias do mês atual
	const monthDays = useMemo(() => {
		const start = startOfMonth(currentMonth);
		const end = endOfMonth(currentMonth);
		return eachDayOfInterval({ start, end });
	}, [currentMonth]);

	// Determinar o tipo de cada dia
	const getDayType = (date: Date) => {
		const isHol = isHoliday(date);
		const isWeekendDay = isWeekend(date);
		const isWorkingDay = calculation?.customWorkingDays 
			? calculation.customWorkingDays.includes(date.getDay())
			: !isWeekendDay;

		// Verificar se está no período de trabalho
		const isInWorkingPeriod = calculation && result 
			? date >= calculation.startDate && date <= result.endDate
			: false;

		if (isHol) return "holiday";
		if (!isWorkingDay) return "weekend";
		if (isInWorkingPeriod && highlightPeriod) return "working";
		return "normal";
	};

	// Obter classes CSS para cada tipo de dia
	const getDayClasses = (date: Date, dayType: string) => {
		const baseClasses = "w-8 h-8 flex items-center justify-center text-sm rounded-md cursor-pointer transition-colors";
		const isCurrentMonth = isSameMonth(date, currentMonth);
		const isTodayDate = isToday(date);

		let typeClasses = "";
		switch (dayType) {
			case "holiday":
				typeClasses = "bg-red-100 text-red-800 hover:bg-red-200";
				break;
			case "weekend":
				typeClasses = "bg-gray-100 text-gray-500 hover:bg-gray-200";
				break;
			case "working":
				typeClasses = "bg-blue-100 text-blue-800 hover:bg-blue-200 font-medium";
				break;
			default:
				typeClasses = "hover:bg-accent";
		}

		return cn(
			baseClasses,
			typeClasses,
			!isCurrentMonth && "opacity-30",
			isTodayDate && "ring-2 ring-primary ring-offset-1",
		);
	};

	// Navegar entre meses
	const goToPreviousMonth = () => setCurrentMonth(prev => subMonths(prev, 1));
	const goToNextMonth = () => setCurrentMonth(prev => addMonths(prev, 1));
	const goToToday = () => setCurrentMonth(new Date());

	// Estatísticas do mês
	const monthStats = useMemo(() => {
		const stats = {
			total: monthDays.length,
			working: 0,
			weekends: 0,
			holidays: 0,
		};

		monthDays.forEach(date => {
			const dayType = getDayType(date);
			switch (dayType) {
				case "holiday":
					stats.holidays++;
					break;
				case "weekend":
					stats.weekends++;
					break;
				case "working":
				case "normal":
					if (calculation?.customWorkingDays 
						? calculation.customWorkingDays.includes(date.getDay())
						: !isWeekend(date)) {
						stats.working++;
					}
					break;
			}
		});

		return stats;
	}, [monthDays, calculation, getDayType]);

	if (compact) {
		return (
			<Card className="w-full">
				<CardHeader className="pb-3">
					<CardTitle className="text-lg flex items-center gap-2">
						<Calendar className="h-5 w-5" />
						{format(currentMonth, "MMMM yyyy", { locale: ptBR })}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-7 gap-1 text-center">
						{/* Cabeçalho dos dias da semana */}
						{["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"].map(day => (
							<div key={day} className="text-xs font-medium text-muted-foreground p-1">
								{day}
							</div>
						))}
						
						{/* Dias do mês */}
						{monthDays.map(date => {
							const dayType = getDayType(date);
							return (
								<div
									key={date.toISOString()}
									className={getDayClasses(date, dayType)}
									onClick={() => onDateSelect?.(date)}
								>
									{format(date, "d")}
								</div>
							);
						})}
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<TooltipProvider>
			<Card className="w-full">
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle className="flex items-center gap-2">
								<Calendar className="h-6 w-6" />
								Calendário de Dias Úteis
							</CardTitle>
							<CardDescription>
								Visualização do período de trabalho e feriados
							</CardDescription>
						</div>
						
						{/* Controles de navegação */}
						<div className="flex items-center gap-2">
							<Button variant="outline" size="sm" onClick={goToPreviousMonth}>
								<ChevronLeft className="h-4 w-4" />
							</Button>
							
							<Button variant="outline" size="sm" onClick={goToToday}>
								Hoje
							</Button>
							
							<Button variant="outline" size="sm" onClick={goToNextMonth}>
								<ChevronRight className="h-4 w-4" />
							</Button>
						</div>
					</div>
				</CardHeader>

				<CardContent className="space-y-6">
					{/* Cabeçalho do mês */}
					<div className="text-center">
						<h3 className="text-2xl font-semibold">
							{format(currentMonth, "MMMM yyyy", { locale: ptBR })}
						</h3>
					</div>

					{/* Estatísticas do mês */}
					<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
						<div className="text-center">
							<div className="text-2xl font-bold">{monthStats.total}</div>
							<div className="text-sm text-muted-foreground">Total de dias</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-blue-600">{monthStats.working}</div>
							<div className="text-sm text-muted-foreground">Dias úteis</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-gray-500">{monthStats.weekends}</div>
							<div className="text-sm text-muted-foreground">Fins de semana</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-red-600">{monthStats.holidays}</div>
							<div className="text-sm text-muted-foreground">Feriados</div>
						</div>
					</div>

					{/* Calendário */}
					<div className="space-y-4">
						{/* Cabeçalho dos dias da semana */}
						<div className="grid grid-cols-7 gap-2 text-center">
							{["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"].map(day => (
								<div key={day} className="text-sm font-medium text-muted-foreground p-2">
									{day}
								</div>
							))}
						</div>

						{/* Dias do mês */}
						<div className="grid grid-cols-7 gap-2">
							{monthDays.map(date => {
								const dayType = getDayType(date);
								const holiday = isHoliday(date);
								
								return (
									<Tooltip key={date.toISOString()}>
										<TooltipTrigger asChild>
											<div
												className={getDayClasses(date, dayType)}
												onClick={() => onDateSelect?.(date)}
											>
												{format(date, "d")}
											</div>
										</TooltipTrigger>
										<TooltipContent>
											<div className="space-y-1">
												<div className="font-medium">
													{format(date, "EEEE, dd 'de' MMMM", { locale: ptBR })}
												</div>
												<div className="text-sm">
													{dayType === "holiday" && "🎉 Feriado"}
													{dayType === "weekend" && "🏠 Fim de semana"}
													{dayType === "working" && "💼 Dia útil (período de trabalho)"}
													{dayType === "normal" && "📅 Dia normal"}
												</div>
												{holiday && (
													<div className="text-xs text-muted-foreground">
														{/* Nome do feriado seria exibido aqui */}
													</div>
												)}
											</div>
										</TooltipContent>
									</Tooltip>
								);
							})}
						</div>
					</div>

					{/* Legenda */}
					{showLegend && (
						<div className="space-y-3">
							<div className="flex items-center gap-2">
								<Info className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">Legenda</span>
							</div>
							
							<div className="grid grid-cols-2 md:grid-cols-4 gap-3">
								<div className="flex items-center gap-2">
									<div className="w-4 h-4 bg-blue-100 rounded border"></div>
									<span className="text-sm">Período de trabalho</span>
								</div>
								<div className="flex items-center gap-2">
									<div className="w-4 h-4 bg-red-100 rounded border"></div>
									<span className="text-sm">Feriados</span>
								</div>
								<div className="flex items-center gap-2">
									<div className="w-4 h-4 bg-gray-100 rounded border"></div>
									<span className="text-sm">Fins de semana</span>
								</div>
								<div className="flex items-center gap-2">
									<div className="w-4 h-4 ring-2 ring-primary rounded"></div>
									<span className="text-sm">Hoje</span>
								</div>
							</div>
						</div>
					)}

					{/* Informações do período */}
					{calculation && result && (
						<div className="bg-muted/50 rounded-lg p-4 space-y-2">
							<div className="font-medium">Período Calculado</div>
							<div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
								<div>
									<span className="text-muted-foreground">Início:</span>
									<div className="font-medium">
										{format(calculation.startDate, "dd/MM/yyyy", { locale: ptBR })}
									</div>
								</div>
								<div>
									<span className="text-muted-foreground">Fim:</span>
									<div className="font-medium">
										{format(result.endDate, "dd/MM/yyyy", { locale: ptBR })}
									</div>
								</div>
								<div>
									<span className="text-muted-foreground">Dias úteis:</span>
									<div className="font-medium">
										{result.workingDaysCount} dias
									</div>
								</div>
							</div>
						</div>
					)}
				</CardContent>
			</Card>
		</TooltipProvider>
	);
}

"use client";

import { useState, useMemo } from "react";
import { addDays, format, isWeekend, differenceInDays } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Calculator, Clock, CalendarDays, AlertCircle } from "lucide-react";
import { holidayService } from "@/lib/holidays";

interface IWorkingDaysCalculatorHook {
	startDate: Date | undefined;
	workingDays: number;
	endDate: Date | undefined;
	isCalculating: boolean;
}

interface IWorkingDaysCalculatorActions {
	setStartDate: (date: Date | undefined) => void;
	setWorkingDays: (days: number) => void;
	calculateEndDate: () => void;
	resetCalculator: () => void;
}

export default function Home() {
	const [state, setState] = useState<IWorkingDaysCalculatorHook>({
		startDate: undefined,
		workingDays: 0,
		endDate: undefined,
		isCalculating: false,
	});

	// Calcular estatísticas quando temos data final
	const periodStats = useMemo(() => {
		if (state.startDate && state.endDate) {
			return getPeriodStats(state.startDate, state.endDate);
		}
		return null;
	}, [state.startDate, state.endDate]);

	// Obter feriados do ano atual
	const currentYearHolidays = useMemo(() => {
		const year = state.startDate?.getFullYear() || new Date().getFullYear();
		return holidayService.getHolidaysForYear(year);
	}, [state.startDate]);

	const actions: IWorkingDaysCalculatorActions = {
		setStartDate: (date: Date | undefined) => {
			setState(prev => ({ ...prev, startDate: date, endDate: undefined }));
		},

		setWorkingDays: (days: number) => {
			setState(prev => ({ ...prev, workingDays: days, endDate: undefined }));
		},

		calculateEndDate: () => {
			if (!state.startDate || state.workingDays <= 0) return;

			setState(prev => ({ ...prev, isCalculating: true }));

			// Simular processamento assíncrono para melhor UX
			setTimeout(() => {
				const endDate = calculateWorkingDaysEndDate(state.startDate!, state.workingDays);
				setState(prev => ({
					...prev,
					endDate,
					isCalculating: false,
				}));
			}, 500);
		},

		resetCalculator: () => {
			setState({
				startDate: undefined,
				workingDays: 0,
				endDate: undefined,
				isCalculating: false,
			});
		},
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
			<div className="max-w-4xl mx-auto">
				<div className="text-center mb-8">
					<h1 className="text-4xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-2">
						<Calculator className="h-8 w-8 text-blue-600" />
						Contador de Dias Úteis
					</h1>
					<p className="text-gray-600 text-lg">
						Calcule a data final considerando apenas dias úteis (excluindo finais de semana e feriados)
					</p>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{/* Painel de Entrada */}
					<Card className="shadow-lg">
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<CalendarIcon className="h-5 w-5" />
								Dados de Entrada
							</CardTitle>
							<CardDescription>Insira a data inicial e a quantidade de dias úteis</CardDescription>
						</CardHeader>
						<CardContent className="space-y-6">
							<div className="space-y-2">
								<label className="text-sm font-medium text-gray-700">Data Inicial</label>
								<Calendar
									mode="single"
									selected={state.startDate}
									onSelect={actions.setStartDate}
									className="rounded-md border"
									locale={ptBR}
								/>
							</div>

							<div className="space-y-2">
								<label className="text-sm font-medium text-gray-700">Quantidade de Dias Úteis</label>
								<Input
									type="number"
									min="1"
									value={state.workingDays || ""}
									onChange={e => actions.setWorkingDays(parseInt(e.target.value) || 0)}
									placeholder="Ex: 10"
									className="w-full"
								/>
							</div>

							<div className="flex gap-2">
								<Button
									onClick={actions.calculateEndDate}
									disabled={!state.startDate || state.workingDays <= 0 || state.isCalculating}
									className="flex-1"
								>
									{state.isCalculating ? "Calculando..." : "Calcular Data Final"}
								</Button>
								<Button variant="outline" onClick={actions.resetCalculator} className="px-4">
									Limpar
								</Button>
							</div>
						</CardContent>
					</Card>

					{/* Painel de Resultado */}
					<Card className="shadow-lg">
						<CardHeader>
							<CardTitle>Resultado</CardTitle>
							<CardDescription>Data final calculada considerando apenas dias úteis</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							{state.startDate && (
								<div className="p-4 bg-blue-50 rounded-lg">
									<div className="text-sm text-gray-600 mb-1">Data Inicial:</div>
									<div className="font-semibold text-blue-900">
										{format(state.startDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
									</div>
								</div>
							)}

							{state.workingDays > 0 && (
								<div className="p-4 bg-green-50 rounded-lg">
									<div className="text-sm text-gray-600 mb-1">Dias Úteis:</div>
									<div className="font-semibold text-green-900">
										{state.workingDays} {state.workingDays === 1 ? "dia" : "dias"}
									</div>
								</div>
							)}

							{state.endDate && (
								<div className="p-4 bg-purple-50 rounded-lg border-2 border-purple-200">
									<div className="text-sm text-gray-600 mb-1">Data Final:</div>
									<div className="font-bold text-xl text-purple-900">
										{format(state.endDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
									</div>
									<div className="text-sm text-purple-700 mt-1">{format(state.endDate, "EEEE", { locale: ptBR })}</div>
								</div>
							)}

							{!state.endDate && state.startDate && state.workingDays > 0 && (
								<div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
									Clique em &quot;Calcular Data Final&quot; para ver o resultado
								</div>
							)}

							{!state.startDate && !state.workingDays && (
								<div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">Preencha os dados para começar o cálculo</div>
							)}
						</CardContent>
					</Card>
				</div>

				{/* Informações Adicionais */}
				<Card className="mt-6 shadow-lg">
					<CardHeader>
						<CardTitle>Como Funciona</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
							<div className="p-3 bg-blue-50 rounded-lg">
								<div className="font-semibold text-blue-900 mb-1">Dias Úteis</div>
								<div className="text-blue-700">Segunda a Sexta-feira</div>
							</div>
							<div className="p-3 bg-red-50 rounded-lg">
								<div className="font-semibold text-red-900 mb-1">Excluídos</div>
								<div className="text-red-700">Sábados, Domingos e Feriados</div>
							</div>
							<div className="p-3 bg-green-50 rounded-lg">
								<div className="font-semibold text-green-900 mb-1">Cálculo</div>
								<div className="text-green-700">Contagem progressiva apenas em dias úteis</div>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}

// Função utilitária para calcular a data final considerando apenas dias úteis
function calculateWorkingDaysEndDate(startDate: Date, workingDays: number): Date {
	let currentDate = new Date(startDate);
	let daysAdded = 0;

	while (daysAdded < workingDays) {
		currentDate = addDays(currentDate, 1);

		// Verifica se é um dia útil (não é fim de semana e não é feriado)
		if (!isWeekend(currentDate) && !holidayService.isHoliday(currentDate)) {
			daysAdded++;
		}
	}

	return currentDate;
}

// Função para obter estatísticas do período
function getPeriodStats(startDate: Date, endDate: Date) {
	const totalDays = differenceInDays(endDate, startDate) + 1;
	let workingDays = 0;
	let weekends = 0;
	let holidays = 0;

	let currentDate = new Date(startDate);

	for (let i = 0; i < totalDays; i++) {
		if (isWeekend(currentDate)) {
			weekends++;
		} else if (holidayService.isHoliday(currentDate)) {
			holidays++;
		} else {
			workingDays++;
		}
		currentDate = addDays(currentDate, 1);
	}

	return { totalDays, workingDays, weekends, holidays };
}

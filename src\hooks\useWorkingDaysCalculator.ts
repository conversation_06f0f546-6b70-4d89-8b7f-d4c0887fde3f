import { useState, useCallback, useMemo } from "react";
import { 
	IWorkingDaysCalculatorHook, 
	IWorkingDaysCalculation, 
	ICalculationResult,
	IPeriodStats 
} from "@/types";
import { calculationService } from "@/services/calculationService";
import { storageService } from "@/services/storageService";
import { useNotifications } from "./useNotifications";

const DEFAULT_CALCULATION: IWorkingDaysCalculation = {
	startDate: new Date(),
	workingDays: 0,
	includeStartDate: true,
	excludeHolidays: true,
	customWorkingDays: [1, 2, 3, 4, 5], // Segunda a Sexta
};

export function useWorkingDaysCalculator(): IWorkingDaysCalculatorHook {
	const [calculation, setCalculation] = useState<IWorkingDaysCalculation>(DEFAULT_CALCULATION);
	const [result, setResult] = useState<ICalculationResult | null>(null);
	const [isCalculating, setIsCalculating] = useState(false);
	const [error, setError] = useState<string | null>(null);
	
	const { addNotification } = useNotifications();

	// Validação se pode calcular
	const canCalculate = useMemo(() => {
		return calculation.startDate && calculation.workingDays > 0;
	}, [calculation.startDate, calculation.workingDays]);

	// Estatísticas do período (quando há resultado)
	const periodStats = useMemo(() => {
		return result?.periodStats || null;
	}, [result]);

	// Atualizar cálculo
	const updateCalculation = useCallback((updates: Partial<IWorkingDaysCalculation>) => {
		setCalculation(prev => ({ ...prev, ...updates }));
		setResult(null); // Limpar resultado anterior
		setError(null);
	}, []);

	// Executar cálculo
	const calculate = useCallback(async () => {
		if (!canCalculate) {
			setError("Dados insuficientes para realizar o cálculo");
			return;
		}

		setIsCalculating(true);
		setError(null);

		try {
			// Simular processamento assíncrono para melhor UX
			await new Promise(resolve => setTimeout(resolve, 300));
			
			const calculationResult = calculationService.calculateEndDate(calculation);
			setResult(calculationResult);
			
			addNotification({
				type: "success",
				title: "Cálculo realizado",
				message: `Data final: ${calculationResult.endDate.toLocaleDateString("pt-BR")}`,
				duration: 3000,
			});

			// Auto-salvar no histórico se habilitado
			const preferences = storageService.getPreferences();
			if (preferences.autoSaveCalculations) {
				storageService.addToHistory({
					calculation,
					result: calculationResult,
				});
			}
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro desconhecido no cálculo";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro no cálculo",
				message: errorMessage,
				duration: 5000,
			});
		} finally {
			setIsCalculating(false);
		}
	}, [calculation, canCalculate, addNotification]);

	// Resetar calculadora
	const reset = useCallback(() => {
		setCalculation(DEFAULT_CALCULATION);
		setResult(null);
		setError(null);
		setIsCalculating(false);
	}, []);

	// Salvar no histórico manualmente
	const saveToHistory = useCallback((notes?: string) => {
		if (!result) {
			addNotification({
				type: "warning",
				title: "Nenhum resultado",
				message: "Execute um cálculo antes de salvar no histórico",
				duration: 3000,
			});
			return;
		}

		try {
			storageService.addToHistory({
				calculation,
				result,
				notes,
			});
			
			addNotification({
				type: "success",
				title: "Salvo no histórico",
				message: "Cálculo adicionado ao histórico com sucesso",
				duration: 3000,
			});
		} catch (err) {
			addNotification({
				type: "error",
				title: "Erro ao salvar",
				message: "Não foi possível salvar no histórico",
				duration: 3000,
			});
		}
	}, [calculation, result, addNotification]);

	return {
		calculation,
		result,
		isCalculating,
		error,
		updateCalculation,
		calculate,
		reset,
		saveToHistory,
		periodStats,
		canCalculate,
	};
}

// Hook para gerenciar múltiplos cálculos (comparações)
export function useMultipleCalculations() {
	const [calculations, setCalculations] = useState<Array<{
		id: string;
		name: string;
		calculation: IWorkingDaysCalculation;
		result: ICalculationResult | null;
	}>>([]);

	const addCalculation = useCallback((name: string, calculation: IWorkingDaysCalculation) => {
		const id = `calc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
		const result = calculationService.calculateEndDate(calculation);
		
		setCalculations(prev => [...prev, {
			id,
			name,
			calculation,
			result,
		}]);
		
		return id;
	}, []);

	const removeCalculation = useCallback((id: string) => {
		setCalculations(prev => prev.filter(calc => calc.id !== id));
	}, []);

	const updateCalculation = useCallback((id: string, updates: Partial<IWorkingDaysCalculation>) => {
		setCalculations(prev => prev.map(calc => {
			if (calc.id === id) {
				const updatedCalculation = { ...calc.calculation, ...updates };
				const result = calculationService.calculateEndDate(updatedCalculation);
				return {
					...calc,
					calculation: updatedCalculation,
					result,
				};
			}
			return calc;
		}));
	}, []);

	const clearAll = useCallback(() => {
		setCalculations([]);
	}, []);

	return {
		calculations,
		addCalculation,
		removeCalculation,
		updateCalculation,
		clearAll,
	};
}

// Hook para análise de cenários
export function useScenarioAnalysis() {
	const [baseCalculation, setBaseCalculation] = useState<IWorkingDaysCalculation>(DEFAULT_CALCULATION);
	const [scenarios, setScenarios] = useState<Array<{
		name: string;
		modifier: Partial<IWorkingDaysCalculation>;
		result: ICalculationResult;
	}>>([]);

	const updateBase = useCallback((calculation: IWorkingDaysCalculation) => {
		setBaseCalculation(calculation);
		// Recalcular todos os cenários
		setScenarios(prev => prev.map(scenario => ({
			...scenario,
			result: calculationService.calculateEndDate({ ...calculation, ...scenario.modifier }),
		})));
	}, []);

	const addScenario = useCallback((name: string, modifier: Partial<IWorkingDaysCalculation>) => {
		const result = calculationService.calculateEndDate({ ...baseCalculation, ...modifier });
		setScenarios(prev => [...prev, { name, modifier, result }]);
	}, [baseCalculation]);

	const removeScenario = useCallback((index: number) => {
		setScenarios(prev => prev.filter((_, i) => i !== index));
	}, []);

	return {
		baseCalculation,
		scenarios,
		updateBase,
		addScenario,
		removeScenario,
	};
}

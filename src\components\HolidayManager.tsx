"use client";

import React, { useState } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { 
	Calendar, 
	Plus, 
	Trash2, 
	Download, 
	Upload, 
	Settings,
	Filter,
	Search
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useHolidayManager, useHolidayImportExport } from "@/hooks/useHolidayManager";
import { IHoliday } from "@/types";

interface IHolidayManagerProps {
	onHolidaySelect?: (holiday: IHoliday) => void;
	selectedYear?: number;
	compact?: boolean;
}

export function HolidayManager({ 
	onHolidaySelect, 
	selectedYear = new Date().getFullYear(),
	compact = false 
}: IHolidayManagerProps) {
	const {
		holidays,
		configuration,
		isLoading,
		error,
		addCustomHoliday,
		removeCustomHoliday,
		updateConfiguration,
		getHolidaysForYear,
	} = useHolidayManager();

	const { exportHolidays, importHolidays } = useHolidayImportExport();

	const [selectedDate, setSelectedDate] = useState<Date | undefined>();
	const [newHolidayName, setNewHolidayName] = useState("");
	const [newHolidayDescription, setNewHolidayDescription] = useState("");
	const [filterType, setFilterType] = useState<string>("all");
	const [searchTerm, setSearchTerm] = useState("");
	const [showAddForm, setShowAddForm] = useState(false);

	// Filtrar feriados baseado no ano, tipo e busca
	const filteredHolidays = React.useMemo(() => {
		let filtered = getHolidaysForYear(selectedYear);

		if (filterType !== "all") {
			filtered = filtered.filter(h => h.type === filterType);
		}

		if (searchTerm) {
			filtered = filtered.filter(h => 
				h.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				h.description?.toLowerCase().includes(searchTerm.toLowerCase())
			);
		}

		return filtered.sort((a, b) => a.date.localeCompare(b.date));
	}, [selectedYear, filterType, searchTerm, getHolidaysForYear]);

	// Adicionar novo feriado
	const handleAddHoliday = () => {
		if (!selectedDate || !newHolidayName.trim()) return;

		addCustomHoliday({
			date: format(selectedDate, "yyyy-MM-dd"),
			name: newHolidayName.trim(),
			description: newHolidayDescription.trim() || undefined,
		});

		// Limpar formulário
		setSelectedDate(undefined);
		setNewHolidayName("");
		setNewHolidayDescription("");
		setShowAddForm(false);
	};

	// Remover feriado personalizado
	const handleRemoveHoliday = (holiday: IHoliday) => {
		if (holiday.type === "personalizado") {
			removeCustomHoliday(holiday.date);
		}
	};

	// Exportar feriados
	const handleExport = (format: "json" | "csv" | "ical") => {
		exportHolidays(filteredHolidays, format);
	};

	// Importar feriados
	const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (!file) return;

		try {
			const importedHolidays = await importHolidays(file);
			importedHolidays.forEach(holiday => {
				addCustomHoliday(holiday);
			});
		} catch (error) {
			console.error("Erro na importação:", error);
		}

		// Limpar input
		event.target.value = "";
	};

	if (compact) {
		return (
			<Card className="w-full">
				<CardHeader className="pb-3">
					<CardTitle className="text-lg flex items-center gap-2">
						<Calendar className="h-5 w-5" />
						Feriados {selectedYear}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-2 max-h-60 overflow-y-auto">
						{filteredHolidays.map((holiday) => (
							<div
								key={holiday.date}
								className="flex items-center justify-between p-2 rounded-lg border hover:bg-accent cursor-pointer"
								onClick={() => onHolidaySelect?.(holiday)}
							>
								<div>
									<div className="font-medium text-sm">{holiday.name}</div>
									<div className="text-xs text-muted-foreground">
										{format(new Date(holiday.date), "dd 'de' MMMM", { locale: ptBR })}
									</div>
								</div>
								<div className={`text-xs px-2 py-1 rounded-full ${
									holiday.type === "nacional" ? "bg-blue-100 text-blue-800" :
									holiday.type === "estadual" ? "bg-green-100 text-green-800" :
									holiday.type === "municipal" ? "bg-yellow-100 text-yellow-800" :
									"bg-purple-100 text-purple-800"
								}`}>
									{holiday.type}
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<div className="space-y-6">
			{/* Cabeçalho com controles */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Calendar className="h-6 w-6" />
						Gerenciamento de Feriados
					</CardTitle>
					<CardDescription>
						Configure feriados personalizados e gerencie configurações regionais
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="flex flex-wrap gap-4 items-center">
						{/* Busca */}
						<div className="flex-1 min-w-64">
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
								<Input
									placeholder="Buscar feriados..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="pl-10"
								/>
							</div>
						</div>

						{/* Filtro por tipo */}
						<Select value={filterType} onValueChange={setFilterType}>
							<SelectTrigger className="w-48">
								<Filter className="h-4 w-4 mr-2" />
								<SelectValue placeholder="Filtrar por tipo" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Todos os tipos</SelectItem>
								<SelectItem value="nacional">Nacional</SelectItem>
								<SelectItem value="estadual">Estadual</SelectItem>
								<SelectItem value="municipal">Municipal</SelectItem>
								<SelectItem value="personalizado">Personalizado</SelectItem>
							</SelectContent>
						</Select>

						{/* Botões de ação */}
						<div className="flex gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={() => setShowAddForm(!showAddForm)}
							>
								<Plus className="h-4 w-4 mr-2" />
								Adicionar
							</Button>

							<Button
								variant="outline"
								size="sm"
								onClick={() => handleExport("json")}
							>
								<Download className="h-4 w-4 mr-2" />
								Exportar
							</Button>

							<label className="cursor-pointer">
								<Button variant="outline" size="sm" asChild>
									<span>
										<Upload className="h-4 w-4 mr-2" />
										Importar
									</span>
								</Button>
								<input
									type="file"
									accept=".json,.csv"
									onChange={handleImport}
									className="hidden"
								/>
							</label>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Formulário de adição */}
			{showAddForm && (
				<Card>
					<CardHeader>
						<CardTitle className="text-lg">Adicionar Feriado Personalizado</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
							<div className="space-y-4">
								<div>
									<label className="text-sm font-medium mb-2 block">Data do Feriado</label>
									<CalendarComponent
										mode="single"
										selected={selectedDate}
										onSelect={setSelectedDate}
										className="rounded-md border"
										locale={ptBR}
									/>
								</div>
							</div>

							<div className="space-y-4">
								<div>
									<label className="text-sm font-medium mb-2 block">Nome do Feriado</label>
									<Input
										value={newHolidayName}
										onChange={(e) => setNewHolidayName(e.target.value)}
										placeholder="Ex: Dia da Empresa"
									/>
								</div>

								<div>
									<label className="text-sm font-medium mb-2 block">Descrição (opcional)</label>
									<Input
										value={newHolidayDescription}
										onChange={(e) => setNewHolidayDescription(e.target.value)}
										placeholder="Descrição adicional do feriado"
									/>
								</div>

								<div className="flex gap-2 pt-4">
									<Button
										onClick={handleAddHoliday}
										disabled={!selectedDate || !newHolidayName.trim()}
										className="flex-1"
									>
										Adicionar Feriado
									</Button>
									<Button
										variant="outline"
										onClick={() => setShowAddForm(false)}
									>
										Cancelar
									</Button>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			)}

			{/* Lista de feriados */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg">
						Feriados de {selectedYear} ({filteredHolidays.length})
					</CardTitle>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="text-center py-8 text-muted-foreground">
							Carregando feriados...
						</div>
					) : error ? (
						<div className="text-center py-8 text-destructive">
							Erro: {error}
						</div>
					) : filteredHolidays.length === 0 ? (
						<div className="text-center py-8 text-muted-foreground">
							Nenhum feriado encontrado para os filtros selecionados
						</div>
					) : (
						<div className="space-y-2">
							{filteredHolidays.map((holiday) => (
								<div
									key={holiday.date}
									className="flex items-center justify-between p-4 rounded-lg border hover:bg-accent"
								>
									<div className="flex-1">
										<div className="font-medium">{holiday.name}</div>
										<div className="text-sm text-muted-foreground">
											{format(new Date(holiday.date), "EEEE, dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
										</div>
										{holiday.description && (
											<div className="text-sm text-muted-foreground mt-1">
												{holiday.description}
											</div>
										)}
									</div>

									<div className="flex items-center gap-3">
										<div className={`text-xs px-3 py-1 rounded-full font-medium ${
											holiday.type === "nacional" ? "bg-blue-100 text-blue-800" :
											holiday.type === "estadual" ? "bg-green-100 text-green-800" :
											holiday.type === "municipal" ? "bg-yellow-100 text-yellow-800" :
											"bg-purple-100 text-purple-800"
										}`}>
											{holiday.type}
										</div>

										{holiday.type === "personalizado" && (
											<Button
												variant="ghost"
												size="sm"
												onClick={() => handleRemoveHoliday(holiday)}
												className="text-destructive hover:text-destructive"
											>
												<Trash2 className="h-4 w-4" />
											</Button>
										)}
									</div>
								</div>
							))}
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}

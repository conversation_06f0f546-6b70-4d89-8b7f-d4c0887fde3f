"use client";

import React, { useState } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { 
	BarChart3, 
	TrendingUp, 
	AlertTriangle, 
	Target, 
	Zap,
	Calendar,
	Clock,
	CheckCircle,
	XCircle,
	Info
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { useAdvancedCalculations, useScenarioAnalysis, useRiskAnalysis } from "@/hooks/useAdvancedCalculations";
import { IWorkingDaysCalculation, IProjectTemplate } from "@/types";

interface IAdvancedAnalysisProps {
	calculation?: IWorkingDaysCalculation;
	template?: IProjectTemplate;
	onAnalysisComplete?: (analysis: any) => void;
}

export function AdvancedAnalysis({
	calculation,
	template,
	onAnalysisComplete
}: IAdvancedAnalysisProps) {
	const [activeTab, setActiveTab] = useState("scenarios");
	
	const {
		isLoading,
		error,
		scenarioComparison,
		periodAnalysis,
		estimateAnalysis,
		analyzePeriod,
		generateEstimates,
		calculateTemplateEstimate,
		clearResults
	} = useAdvancedCalculations();

	const {
		analyzeCommonScenarios,
		scenarioComparison: scenarioAnalysis
	} = useScenarioAnalysis();

	const {
		analyzeProjectRisks,
		generateRiskRecommendations
	} = useRiskAnalysis();

	// Executar análise de cenários
	const handleScenarioAnalysis = async () => {
		if (!calculation) return;
		await analyzeCommonScenarios(calculation);
		onAnalysisComplete?.(scenarioAnalysis);
	};

	// Executar análise de período
	const handlePeriodAnalysis = async () => {
		if (!calculation) return;
		
		const endDate = new Date(calculation.startDate);
		endDate.setDate(endDate.getDate() + calculation.workingDays + 30); // Adicionar margem
		
		await analyzePeriod(calculation.startDate, endDate, calculation.customWorkingDays);
		onAnalysisComplete?.(periodAnalysis);
	};

	// Executar análise de estimativas
	const handleEstimateAnalysis = async () => {
		if (!calculation) return;
		await generateEstimates(calculation);
		onAnalysisComplete?.(estimateAnalysis);
	};

	// Executar análise de template
	const handleTemplateAnalysis = async () => {
		if (!template || !calculation) return;
		await calculateTemplateEstimate(template, calculation.startDate);
		onAnalysisComplete?.(estimateAnalysis);
	};

	// Análise de riscos
	const projectRisks = calculation ? analyzeProjectRisks(calculation) : [];
	const riskRecommendations = generateRiskRecommendations(projectRisks);

	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<BarChart3 className="h-6 w-6" />
					Análise Avançada
				</CardTitle>
				<CardDescription>
					Análises detalhadas, comparações de cenários e estimativas de risco
				</CardDescription>
			</CardHeader>

			<CardContent>
				<Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
					<TabsList className="grid w-full grid-cols-4">
						<TabsTrigger value="scenarios">Cenários</TabsTrigger>
						<TabsTrigger value="period">Período</TabsTrigger>
						<TabsTrigger value="estimates">Estimativas</TabsTrigger>
						<TabsTrigger value="risks">Riscos</TabsTrigger>
					</TabsList>

					{/* Análise de Cenários */}
					<TabsContent value="scenarios" className="space-y-4">
						<div className="flex items-center justify-between">
							<div>
								<h3 className="text-lg font-semibold">Comparação de Cenários</h3>
								<p className="text-sm text-muted-foreground">
									Compare diferentes abordagens para o mesmo projeto
								</p>
							</div>
							<Button 
								onClick={handleScenarioAnalysis}
								disabled={!calculation || isLoading}
								className="flex items-center gap-2"
							>
								<Zap className="h-4 w-4" />
								Analisar Cenários
							</Button>
						</div>

						{scenarioComparison && (
							<div className="space-y-4">
								{/* Estatísticas gerais */}
								<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
									<Card>
										<CardContent className="pt-6">
											<div className="text-2xl font-bold text-green-600">
												{scenarioComparison.fastest.result.workingDaysCount}
											</div>
											<p className="text-xs text-muted-foreground">Cenário mais rápido</p>
										</CardContent>
									</Card>
									<Card>
										<CardContent className="pt-6">
											<div className="text-2xl font-bold text-red-600">
												{scenarioComparison.slowest.result.workingDaysCount}
											</div>
											<p className="text-xs text-muted-foreground">Cenário mais lento</p>
										</CardContent>
									</Card>
									<Card>
										<CardContent className="pt-6">
											<div className="text-2xl font-bold">
												{scenarioComparison.statistics.average}
											</div>
											<p className="text-xs text-muted-foreground">Média</p>
										</CardContent>
									</Card>
									<Card>
										<CardContent className="pt-6">
											<div className="text-2xl font-bold">
												{scenarioComparison.statistics.range}
											</div>
											<p className="text-xs text-muted-foreground">Variação</p>
										</CardContent>
									</Card>
								</div>

								{/* Lista de cenários */}
								<div className="space-y-3">
									{scenarioComparison.scenarios.map((scenario, index) => (
										<div key={index} className="flex items-center justify-between p-4 border rounded-lg">
											<div className="flex-1">
												<div className="font-medium">{scenario.scenarioName}</div>
												<div className="text-sm text-muted-foreground">
													{scenario.result.workingDaysCount} dias úteis
												</div>
											</div>
											<div className="flex items-center gap-2">
												{scenario === scenarioComparison.fastest && (
													<Badge variant="default" className="bg-green-100 text-green-800">
														Mais rápido
													</Badge>
												)}
												{scenario === scenarioComparison.slowest && (
													<Badge variant="destructive">
														Mais lento
													</Badge>
												)}
											</div>
										</div>
									))}
								</div>

								{/* Recomendações */}
								{scenarioComparison.recommendations.length > 0 && (
									<Alert>
										<Info className="h-4 w-4" />
										<AlertTitle>Recomendações</AlertTitle>
										<AlertDescription>
											<ul className="list-disc list-inside space-y-1">
												{scenarioComparison.recommendations.map((rec, index) => (
													<li key={index}>{rec}</li>
												))}
											</ul>
										</AlertDescription>
									</Alert>
								)}
							</div>
						)}
					</TabsContent>

					{/* Análise de Período */}
					<TabsContent value="period" className="space-y-4">
						<div className="flex items-center justify-between">
							<div>
								<h3 className="text-lg font-semibold">Análise de Período</h3>
								<p className="text-sm text-muted-foreground">
									Análise detalhada da distribuição de dias no período
								</p>
							</div>
							<Button 
								onClick={handlePeriodAnalysis}
								disabled={!calculation || isLoading}
								className="flex items-center gap-2"
							>
								<Calendar className="h-4 w-4" />
								Analisar Período
							</Button>
						</div>

						{periodAnalysis && (
							<div className="space-y-4">
								{/* Resumo do período */}
								<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
									<Card>
										<CardContent className="pt-6">
											<div className="text-2xl font-bold">
												{periodAnalysis.summary.totalWorkingDays}
											</div>
											<p className="text-xs text-muted-foreground">Dias úteis totais</p>
											<div className="text-xs text-green-600">
												{periodAnalysis.summary.workingDaysPercentage.toFixed(1)}% do período
											</div>
										</CardContent>
									</Card>
									<Card>
										<CardContent className="pt-6">
											<div className="text-2xl font-bold text-gray-600">
												{periodAnalysis.summary.totalWeekends}
											</div>
											<p className="text-xs text-muted-foreground">Fins de semana</p>
											<div className="text-xs text-gray-500">
												{periodAnalysis.summary.weekendsPercentage.toFixed(1)}% do período
											</div>
										</CardContent>
									</Card>
									<Card>
										<CardContent className="pt-6">
											<div className="text-2xl font-bold text-red-600">
												{periodAnalysis.summary.totalHolidays}
											</div>
											<p className="text-xs text-muted-foreground">Feriados</p>
											<div className="text-xs text-red-500">
												{periodAnalysis.summary.holidaysPercentage.toFixed(1)}% do período
											</div>
										</CardContent>
									</Card>
								</div>

								{/* Breakdown mensal */}
								<div className="space-y-3">
									<h4 className="font-medium">Distribuição Mensal</h4>
									{periodAnalysis.monthlyBreakdown.map((month) => (
										<div key={month.month} className="flex items-center justify-between p-3 border rounded">
											<div>
												<div className="font-medium">{month.monthName}</div>
												<div className="text-sm text-muted-foreground">
													{month.workingDays} dias úteis de {month.totalDays} totais
												</div>
											</div>
											<div className="text-right">
												<div className="text-sm font-medium">
													{((month.workingDays / month.totalDays) * 100).toFixed(1)}%
												</div>
												<Progress 
													value={(month.workingDays / month.totalDays) * 100} 
													className="w-20 h-2 mt-1"
												/>
											</div>
										</div>
									))}
								</div>

								{/* Insights */}
								<Alert>
									<TrendingUp className="h-4 w-4" />
									<AlertTitle>Insights do Período</AlertTitle>
									<AlertDescription>
										<div className="space-y-2">
											<p>
												<strong>Mês com mais dias úteis:</strong> {periodAnalysis.insights.monthWithMostWorkingDays.monthName} 
												({periodAnalysis.insights.monthWithMostWorkingDays.workingDays} dias)
											</p>
											<p>
												<strong>Mês com menos dias úteis:</strong> {periodAnalysis.insights.monthWithLeastWorkingDays.monthName} 
												({periodAnalysis.insights.monthWithLeastWorkingDays.workingDays} dias)
											</p>
											<p>
												<strong>Média mensal:</strong> {periodAnalysis.insights.averageWorkingDaysPerMonth} dias úteis
											</p>
											<p>
												<strong>Variabilidade:</strong> {periodAnalysis.insights.variability}
											</p>
										</div>
									</AlertDescription>
								</Alert>
							</div>
						)}
					</TabsContent>

					{/* Análise de Estimativas */}
					<TabsContent value="estimates" className="space-y-4">
						<div className="flex items-center justify-between">
							<div>
								<h3 className="text-lg font-semibold">Estimativas com Buffer</h3>
								<p className="text-sm text-muted-foreground">
									Análise de estimativas com diferentes níveis de confiança
								</p>
							</div>
							<div className="flex gap-2">
								{template && (
									<Button 
										onClick={handleTemplateAnalysis}
										disabled={!calculation || isLoading}
										variant="outline"
										className="flex items-center gap-2"
									>
										<Target className="h-4 w-4" />
										Usar Template
									</Button>
								)}
								<Button 
									onClick={handleEstimateAnalysis}
									disabled={!calculation || isLoading}
									className="flex items-center gap-2"
								>
									<Clock className="h-4 w-4" />
									Gerar Estimativas
								</Button>
							</div>
						</div>

						{estimateAnalysis && (
							<div className="space-y-4">
								{/* Estimativa base */}
								<Card>
									<CardHeader>
										<CardTitle className="text-base">Estimativa Base</CardTitle>
									</CardHeader>
									<CardContent>
										<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
											<div>
												<div className="text-lg font-bold">
													{estimateAnalysis.baseEstimate.workingDays}
												</div>
												<div className="text-sm text-muted-foreground">Dias úteis</div>
											</div>
											<div>
												<div className="text-lg font-bold">
													{format(estimateAnalysis.baseEstimate.endDate, "dd/MM/yyyy", { locale: ptBR })}
												</div>
												<div className="text-sm text-muted-foreground">Data de conclusão</div>
											</div>
											<div>
												<Badge variant="outline" className="text-red-600">
													{estimateAnalysis.baseEstimate.confidence}
												</Badge>
												<div className="text-sm text-muted-foreground">Confiança</div>
											</div>
											<div>
												<Badge variant="outline" className="text-red-600">
													{estimateAnalysis.baseEstimate.risk}
												</Badge>
												<div className="text-sm text-muted-foreground">Risco</div>
											</div>
										</div>
									</CardContent>
								</Card>

								{/* Estimativas com buffer */}
								<div className="space-y-3">
									<h4 className="font-medium">Cenários com Buffer</h4>
									{estimateAnalysis.estimates.map((estimate) => (
										<div key={estimate.bufferPercentage} className="flex items-center justify-between p-4 border rounded-lg">
											<div className="flex-1">
												<div className="font-medium">
													Buffer de {estimate.bufferPercentage}% (+{estimate.bufferDays} dias)
												</div>
												<div className="text-sm text-muted-foreground">
													{estimate.totalWorkingDays} dias úteis • {format(estimate.endDate, "dd/MM/yyyy", { locale: ptBR })}
												</div>
											</div>
											<div className="flex items-center gap-2">
												<Badge 
													variant={estimate.confidenceLevel === "Alta" ? "default" : "outline"}
													className={cn(
														estimate.confidenceLevel === "Alta" && "bg-green-100 text-green-800",
														estimate.confidenceLevel === "Média" && "bg-yellow-100 text-yellow-800",
														estimate.confidenceLevel === "Baixa" && "bg-red-100 text-red-800"
													)}
												>
													{estimate.confidenceLevel}
												</Badge>
											</div>
										</div>
									))}
								</div>

								{/* Recomendações */}
								{estimateAnalysis.recommendations.length > 0 && (
									<Alert>
										<Target className="h-4 w-4" />
										<AlertTitle>Recomendações</AlertTitle>
										<AlertDescription>
											<ul className="list-disc list-inside space-y-1">
												{estimateAnalysis.recommendations.map((rec, index) => (
													<li key={index}>{rec}</li>
												))}
											</ul>
										</AlertDescription>
									</Alert>
								)}

								{/* Resumo */}
								<Card>
									<CardHeader>
										<CardTitle className="text-base">Resumo das Estimativas</CardTitle>
									</CardHeader>
									<CardContent>
										<div className="space-y-3">
											<div>
												<span className="text-sm text-muted-foreground">Buffer recomendado:</span>
												<div className="font-medium">{estimateAnalysis.summary.recommendedBuffer}%</div>
											</div>
											<div>
												<span className="text-sm text-muted-foreground">Cenário mais provável:</span>
												<div className="font-medium">
													{estimateAnalysis.summary.mostLikelyScenario.totalWorkingDays} dias úteis 
													(+{estimateAnalysis.summary.mostLikelyScenario.bufferDays} dias de buffer)
												</div>
											</div>
											<div>
												<span className="text-sm text-muted-foreground">Cenário conservador:</span>
												<div className="font-medium">
													{estimateAnalysis.summary.conservativeScenario.totalWorkingDays} dias úteis 
													(+{estimateAnalysis.summary.conservativeScenario.bufferDays} dias de buffer)
												</div>
											</div>
										</div>
									</CardContent>
								</Card>
							</div>
						)}
					</TabsContent>

					{/* Análise de Riscos */}
					<TabsContent value="risks" className="space-y-4">
						<div>
							<h3 className="text-lg font-semibold">Análise de Riscos</h3>
							<p className="text-sm text-muted-foreground">
								Identificação de fatores de risco e recomendações
							</p>
						</div>

						{calculation && (
							<div className="space-y-4">
								{/* Fatores de risco */}
								<div className="space-y-3">
									<h4 className="font-medium">Fatores de Risco Identificados</h4>
									{projectRisks.length > 0 ? (
										projectRisks.map((risk, index) => (
											<Alert key={index} className={cn(
												risk.impact === "high" && "border-red-200 bg-red-50",
												risk.impact === "medium" && "border-yellow-200 bg-yellow-50",
												risk.impact === "low" && "border-blue-200 bg-blue-50"
											)}>
												<AlertTriangle className={cn(
													"h-4 w-4",
													risk.impact === "high" && "text-red-600",
													risk.impact === "medium" && "text-yellow-600",
													risk.impact === "low" && "text-blue-600"
												)} />
												<AlertTitle className="flex items-center gap-2">
													{risk.factor}
													<Badge variant="outline" className={cn(
														risk.impact === "high" && "text-red-600",
														risk.impact === "medium" && "text-yellow-600",
														risk.impact === "low" && "text-blue-600"
													)}>
														{risk.impact === "high" ? "Alto" : risk.impact === "medium" ? "Médio" : "Baixo"}
													</Badge>
												</AlertTitle>
												<AlertDescription>
													{risk.description}
												</AlertDescription>
											</Alert>
										))
									) : (
										<Alert>
											<CheckCircle className="h-4 w-4 text-green-600" />
											<AlertTitle>Baixo Risco</AlertTitle>
											<AlertDescription>
												Nenhum fator de risco significativo foi identificado para este projeto.
											</AlertDescription>
										</Alert>
									)}
								</div>

								{/* Recomendações */}
								{riskRecommendations.length > 0 && (
									<div className="space-y-3">
										<h4 className="font-medium">Recomendações</h4>
										<Alert>
											<Info className="h-4 w-4" />
											<AlertTitle>Ações Recomendadas</AlertTitle>
											<AlertDescription>
												<ul className="list-disc list-inside space-y-1">
													{riskRecommendations.map((rec, index) => (
														<li key={index}>{rec}</li>
													))}
												</ul>
											</AlertDescription>
										</Alert>
									</div>
								)}
							</div>
						)}
					</TabsContent>
				</Tabs>

				{/* Loading e Error States */}
				{isLoading && (
					<div className="flex items-center justify-center py-8">
						<div className="text-center">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
							<p className="text-sm text-muted-foreground">Processando análise...</p>
						</div>
					</div>
				)}

				{error && (
					<Alert variant="destructive">
						<XCircle className="h-4 w-4" />
						<AlertTitle>Erro na Análise</AlertTitle>
						<AlertDescription>{error}</AlertDescription>
					</Alert>
				)}

				{/* Ações */}
				<div className="flex items-center justify-between pt-4 border-t">
					<Button variant="outline" onClick={clearResults}>
						Limpar Resultados
					</Button>
					
					<div className="text-sm text-muted-foreground">
						{calculation ? "Pronto para análise" : "Selecione um cálculo para analisar"}
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

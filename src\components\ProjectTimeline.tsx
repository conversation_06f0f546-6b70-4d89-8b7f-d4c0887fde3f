"use client";

import React, { useMemo } from "react";
import { format, addDays, differenceInDays, isWeekend } from "date-fns";
import { ptBR } from "date-fns/locale";
import { 
	Calendar, 
	Clock, 
	CheckCircle, 
	Circle, 
	AlertTriangle,
	Flag,
	Play,
	Pause
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { IProjectTemplate, IProjectPhase, IWorkingDaysCalculation } from "@/types";
import { useHolidayManager } from "@/hooks/useHolidayManager";

interface IProjectTimelineProps {
	template?: IProjectTemplate;
	calculation?: IWorkingDaysCalculation;
	startDate?: Date;
	showProgress?: boolean;
	currentPhase?: string;
	completedPhases?: string[];
}

export function ProjectTimeline({
	template,
	calculation,
	startDate = new Date(),
	showProgress = true,
	currentPhase,
	completedPhases = []
}: IProjectTimelineProps) {
	const { isHoliday } = useHolidayManager();

	// Calcular datas das fases
	const phaseSchedule = useMemo(() => {
		if (!template?.phases) return [];

		const workingDays = calculation?.customWorkingDays || [1, 2, 3, 4, 5];
		const excludeHolidays = calculation?.excludeHolidays ?? true;
		
		let currentDate = new Date(startDate);
		const schedule: Array<IProjectPhase & {
			startDate: Date;
			endDate: Date;
			duration: number;
			status: "completed" | "current" | "pending" | "blocked";
			progress: number;
		}> = [];

		// Função para calcular próximo dia útil
		const getNextWorkingDay = (date: Date): Date => {
			let nextDay = new Date(date);
			while (true) {
				if (workingDays.includes(nextDay.getDay()) && 
					(!excludeHolidays || !isHoliday(nextDay))) {
					break;
				}
				nextDay = addDays(nextDay, 1);
			}
			return nextDay;
		};

		// Função para adicionar dias úteis
		const addWorkingDays = (date: Date, days: number): Date => {
			let result = new Date(date);
			let addedDays = 0;
			
			while (addedDays < days) {
				result = addDays(result, 1);
				if (workingDays.includes(result.getDay()) && 
					(!excludeHolidays || !isHoliday(result))) {
					addedDays++;
				}
			}
			return result;
		};

		// Ordenar fases por dependências
		const sortedPhases = template.phases.slice().sort((a, b) => {
			if (a.dependencies.includes(b.id)) return 1;
			if (b.dependencies.includes(a.id)) return -1;
			return 0;
		});

		const phaseEndDates = new Map<string, Date>();

		sortedPhases.forEach(phase => {
			let phaseStartDate = new Date(currentDate);

			// Verificar dependências
			if (phase.dependencies.length > 0) {
				const dependencyEndDates = phase.dependencies
					.map(depId => phaseEndDates.get(depId))
					.filter(date => date !== undefined) as Date[];
				
				if (dependencyEndDates.length > 0) {
					const latestDependencyEnd = new Date(Math.max(...dependencyEndDates.map(d => d.getTime())));
					phaseStartDate = addDays(latestDependencyEnd, 1);
				}
			}

			// Garantir que começe em um dia útil
			phaseStartDate = getNextWorkingDay(phaseStartDate);
			
			// Calcular data de fim
			const phaseEndDate = addWorkingDays(phaseStartDate, phase.estimatedDays - 1);
			
			// Determinar status
			let status: "completed" | "current" | "pending" | "blocked" = "pending";
			let progress = 0;

			if (completedPhases.includes(phase.id)) {
				status = "completed";
				progress = 100;
			} else if (currentPhase === phase.id) {
				status = "current";
				// Calcular progresso baseado na data atual
				const today = new Date();
				if (today >= phaseStartDate && today <= phaseEndDate) {
					const totalDays = differenceInDays(phaseEndDate, phaseStartDate) + 1;
					const daysPassed = differenceInDays(today, phaseStartDate) + 1;
					progress = Math.min(100, Math.max(0, (daysPassed / totalDays) * 100));
				}
			} else if (phase.dependencies.some(depId => !completedPhases.includes(depId))) {
				status = "blocked";
			}

			schedule.push({
				...phase,
				startDate: phaseStartDate,
				endDate: phaseEndDate,
				duration: differenceInDays(phaseEndDate, phaseStartDate) + 1,
				status,
				progress
			});

			phaseEndDates.set(phase.id, phaseEndDate);
			
			// Atualizar data atual para próxima fase
			if (status === "completed" || status === "current") {
				currentDate = addDays(phaseEndDate, 1);
			}
		});

		return schedule;
	}, [template, calculation, startDate, currentPhase, completedPhases, isHoliday]);

	// Calcular métricas do projeto
	const projectMetrics = useMemo(() => {
		if (phaseSchedule.length === 0) return null;

		const totalPhases = phaseSchedule.length;
		const completedPhasesCount = phaseSchedule.filter(p => p.status === "completed").length;
		const currentPhasesCount = phaseSchedule.filter(p => p.status === "current").length;
		const blockedPhasesCount = phaseSchedule.filter(p => p.status === "blocked").length;

		const projectStart = phaseSchedule[0].startDate;
		const projectEnd = phaseSchedule[phaseSchedule.length - 1].endDate;
		const totalDuration = differenceInDays(projectEnd, projectStart) + 1;

		const overallProgress = totalPhases > 0 
			? (completedPhasesCount + (currentPhasesCount * 0.5)) / totalPhases * 100
			: 0;

		return {
			totalPhases,
			completedPhasesCount,
			currentPhasesCount,
			blockedPhasesCount,
			projectStart,
			projectEnd,
			totalDuration,
			overallProgress
		};
	}, [phaseSchedule]);

	// Obter ícone do status
	const getStatusIcon = (status: string) => {
		switch (status) {
			case "completed":
				return <CheckCircle className="h-4 w-4 text-green-600" />;
			case "current":
				return <Play className="h-4 w-4 text-blue-600" />;
			case "blocked":
				return <Pause className="h-4 w-4 text-red-600" />;
			default:
				return <Circle className="h-4 w-4 text-gray-400" />;
		}
	};

	// Obter cor do status
	const getStatusColor = (status: string) => {
		switch (status) {
			case "completed":
				return "bg-green-100 text-green-800 border-green-200";
			case "current":
				return "bg-blue-100 text-blue-800 border-blue-200";
			case "blocked":
				return "bg-red-100 text-red-800 border-red-200";
			default:
				return "bg-gray-100 text-gray-600 border-gray-200";
		}
	};

	if (!template?.phases || phaseSchedule.length === 0) {
		return (
			<Card>
				<CardContent className="flex items-center justify-center py-8">
					<div className="text-center text-muted-foreground">
						<Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
						<p>Nenhum template selecionado ou fases disponíveis</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Flag className="h-6 w-6" />
					Timeline do Projeto: {template.name}
				</CardTitle>
				<CardDescription>
					Cronograma detalhado das fases do projeto
				</CardDescription>
			</CardHeader>

			<CardContent className="space-y-6">
				{/* Métricas do projeto */}
				{projectMetrics && (
					<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
						<div className="text-center">
							<div className="text-2xl font-bold">{projectMetrics.totalPhases}</div>
							<div className="text-sm text-muted-foreground">Total de fases</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-green-600">{projectMetrics.completedPhasesCount}</div>
							<div className="text-sm text-muted-foreground">Concluídas</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-blue-600">{projectMetrics.currentPhasesCount}</div>
							<div className="text-sm text-muted-foreground">Em andamento</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold">{projectMetrics.totalDuration}</div>
							<div className="text-sm text-muted-foreground">Dias totais</div>
						</div>
					</div>
				)}

				{/* Progresso geral */}
				{showProgress && projectMetrics && (
					<div className="space-y-2">
						<div className="flex items-center justify-between">
							<span className="text-sm font-medium">Progresso Geral</span>
							<span className="text-sm text-muted-foreground">
								{projectMetrics.overallProgress.toFixed(1)}%
							</span>
						</div>
						<Progress value={projectMetrics.overallProgress} className="h-3" />
					</div>
				)}

				{/* Timeline das fases */}
				<div className="space-y-4">
					<div className="text-lg font-semibold">Cronograma das Fases</div>
					
					<div className="space-y-4">
						{phaseSchedule.map((phase, index) => (
							<div key={phase.id} className="relative">
								{/* Linha conectora */}
								{index < phaseSchedule.length - 1 && (
									<div className="absolute left-6 top-12 w-0.5 h-8 bg-border"></div>
								)}
								
								<div className={cn(
									"flex items-start gap-4 p-4 rounded-lg border-2 transition-colors",
									getStatusColor(phase.status)
								)}>
									{/* Ícone do status */}
									<div className="flex-shrink-0 mt-1">
										{getStatusIcon(phase.status)}
									</div>

									{/* Conteúdo da fase */}
									<div className="flex-1 space-y-3">
										<div className="flex items-start justify-between">
											<div>
												<h4 className="font-semibold">{phase.name}</h4>
												{phase.description && (
													<p className="text-sm text-muted-foreground mt-1">
														{phase.description}
													</p>
												)}
											</div>
											
											<div className="flex items-center gap-2">
												<Badge variant="outline" className="text-xs">
													{phase.estimatedDays} dias
												</Badge>
												{phase.isOptional && (
													<Badge variant="secondary" className="text-xs">
														Opcional
													</Badge>
												)}
											</div>
										</div>

										{/* Datas */}
										<div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
											<div>
												<span className="text-muted-foreground">Início:</span>
												<div className="font-medium">
													{format(phase.startDate, "dd/MM/yyyy", { locale: ptBR })}
												</div>
											</div>
											<div>
												<span className="text-muted-foreground">Fim:</span>
												<div className="font-medium">
													{format(phase.endDate, "dd/MM/yyyy", { locale: ptBR })}
												</div>
											</div>
										</div>

										{/* Progresso da fase */}
										{phase.status === "current" && phase.progress > 0 && (
											<div className="space-y-1">
												<div className="flex items-center justify-between text-sm">
													<span>Progresso da fase</span>
													<span>{phase.progress.toFixed(1)}%</span>
												</div>
												<Progress value={phase.progress} className="h-2" />
											</div>
										)}

										{/* Dependências */}
										{phase.dependencies.length > 0 && (
											<div className="text-sm">
												<span className="text-muted-foreground">Depende de:</span>
												<div className="flex flex-wrap gap-1 mt-1">
													{phase.dependencies.map(depId => {
														const depPhase = template.phases?.find(p => p.id === depId);
														return depPhase ? (
															<Badge key={depId} variant="outline" className="text-xs">
																{depPhase.name}
															</Badge>
														) : null;
													})}
												</div>
											</div>
										)}

										{/* Alertas */}
										{phase.status === "blocked" && (
											<div className="flex items-center gap-2 text-sm text-red-600">
												<AlertTriangle className="h-4 w-4" />
												<span>Bloqueada por dependências não concluídas</span>
											</div>
										)}
									</div>
								</div>
							</div>
						))}
					</div>
				</div>

				{/* Resumo do projeto */}
				{projectMetrics && (
					<div className="bg-muted/50 rounded-lg p-4 space-y-2">
						<div className="font-medium">Resumo do Projeto</div>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
							<div>
								<span className="text-muted-foreground">Data de início:</span>
								<div className="font-medium">
									{format(projectMetrics.projectStart, "dd/MM/yyyy", { locale: ptBR })}
								</div>
							</div>
							<div>
								<span className="text-muted-foreground">Data de conclusão:</span>
								<div className="font-medium">
									{format(projectMetrics.projectEnd, "dd/MM/yyyy", { locale: ptBR })}
								</div>
							</div>
							<div>
								<span className="text-muted-foreground">Duração total:</span>
								<div className="font-medium">
									{projectMetrics.totalDuration} dias
								</div>
							</div>
						</div>
					</div>
				)}
			</CardContent>
		</Card>
	);
}

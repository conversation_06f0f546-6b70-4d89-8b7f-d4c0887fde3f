import { 
	IAdvancedCalculationService, 
	IWorkingDaysCalculation, 
	IWorkingDaysResult,
	IScenarioComparison,
	IPeriodAnalysis,
	IEstimateAnalysis,
	IProjectTemplate
} from "@/types";
import { calculationService } from "./calculationService";
import { addDays, differenceInDays, format, startOfMonth, endOfMonth, eachMonthOfInterval } from "date-fns";

class AdvancedCalculationService implements IAdvancedCalculationService {
	
	/**
	 * Compara múltiplos cenários de cálculo
	 */
	compareScenarios(scenarios: IWorkingDaysCalculation[]): IScenarioComparison {
		const results = scenarios.map((scenario, index) => ({
			scenario,
			result: calculationService.calculateWorkingDays(scenario),
			scenarioName: `Cenário ${index + 1}`,
		}));

		// Encontrar o cenário mais rápido e mais lento
		const sortedByDuration = results.sort((a, b) => a.result.workingDaysCount - b.result.workingDaysCount);
		const fastest = sortedByDuration[0];
		const slowest = sortedByDuration[sortedByDuration.length - 1];

		// Calcular estatísticas
		const workingDaysCounts = results.map(r => r.result.workingDaysCount);
		const average = workingDaysCounts.reduce((sum, count) => sum + count, 0) / workingDaysCounts.length;
		const variance = workingDaysCounts.reduce((sum, count) => sum + Math.pow(count - average, 2), 0) / workingDaysCounts.length;
		const standardDeviation = Math.sqrt(variance);

		// Análise de diferenças
		const differences = results.map(result => ({
			scenarioName: result.scenarioName,
			differenceFromAverage: result.result.workingDaysCount - average,
			percentageDifference: ((result.result.workingDaysCount - average) / average) * 100,
		}));

		return {
			scenarios: results,
			fastest,
			slowest,
			statistics: {
				average: Math.round(average),
				median: this.calculateMedian(workingDaysCounts),
				standardDeviation: Math.round(standardDeviation * 100) / 100,
				range: slowest.result.workingDaysCount - fastest.result.workingDaysCount,
			},
			differences,
			recommendations: this.generateScenarioRecommendations(results),
		};
	}

	/**
	 * Analisa um período específico
	 */
	analyzePeriod(startDate: Date, endDate: Date, workingDaysPattern?: number[]): IPeriodAnalysis {
		const totalDays = differenceInDays(endDate, startDate) + 1;
		
		// Análise mensal
		const months = eachMonthOfInterval({ start: startDate, end: endDate });
		const monthlyBreakdown = months.map(month => {
			const monthStart = startOfMonth(month);
			const monthEnd = endOfMonth(month);
			
			// Ajustar para o período solicitado
			const periodStart = monthStart < startDate ? startDate : monthStart;
			const periodEnd = monthEnd > endDate ? endDate : monthEnd;
			
			const calculation: IWorkingDaysCalculation = {
				startDate: periodStart,
				workingDays: differenceInDays(periodEnd, periodStart) + 1,
				includeStartDate: true,
				excludeHolidays: true,
				customWorkingDays: workingDaysPattern,
			};
			
			const result = calculationService.calculateWorkingDays(calculation);
			
			return {
				month: format(month, "yyyy-MM"),
				monthName: format(month, "MMMM yyyy"),
				startDate: periodStart,
				endDate: periodEnd,
				totalDays: differenceInDays(periodEnd, periodStart) + 1,
				workingDays: result.workingDaysCount,
				weekends: result.weekendsCount || 0,
				holidays: result.holidaysInPeriod?.length || 0,
			};
		});

		// Calcular totais
		const totalWorkingDays = monthlyBreakdown.reduce((sum, month) => sum + month.workingDays, 0);
		const totalWeekends = monthlyBreakdown.reduce((sum, month) => sum + month.weekends, 0);
		const totalHolidays = monthlyBreakdown.reduce((sum, month) => sum + month.holidays, 0);

		// Análise de distribuição
		const workingDaysPercentage = (totalWorkingDays / totalDays) * 100;
		const weekendsPercentage = (totalWeekends / totalDays) * 100;
		const holidaysPercentage = (totalHolidays / totalDays) * 100;

		// Identificar mês com mais/menos dias úteis
		const monthWithMostWorkingDays = monthlyBreakdown.reduce((max, month) => 
			month.workingDays > max.workingDays ? month : max
		);
		const monthWithLeastWorkingDays = monthlyBreakdown.reduce((min, month) => 
			month.workingDays < min.workingDays ? month : min
		);

		return {
			period: {
				startDate,
				endDate,
				totalDays,
			},
			summary: {
				totalWorkingDays,
				totalWeekends,
				totalHolidays,
				workingDaysPercentage: Math.round(workingDaysPercentage * 100) / 100,
				weekendsPercentage: Math.round(weekendsPercentage * 100) / 100,
				holidaysPercentage: Math.round(holidaysPercentage * 100) / 100,
			},
			monthlyBreakdown,
			insights: {
				monthWithMostWorkingDays,
				monthWithLeastWorkingDays,
				averageWorkingDaysPerMonth: Math.round((totalWorkingDays / monthlyBreakdown.length) * 100) / 100,
				variability: this.calculateVariability(monthlyBreakdown.map(m => m.workingDays)),
			},
		};
	}

	/**
	 * Gera estimativas com diferentes níveis de confiança
	 */
	generateEstimates(baseCalculation: IWorkingDaysCalculation, bufferPercentages: number[] = [10, 20, 30]): IEstimateAnalysis {
		const baseResult = calculationService.calculateWorkingDays(baseCalculation);
		
		const estimates = bufferPercentages.map(bufferPercentage => {
			const bufferDays = Math.ceil((baseResult.workingDaysCount * bufferPercentage) / 100);
			const totalDaysWithBuffer = baseResult.workingDaysCount + bufferDays;
			
			// Calcular nova data de fim com buffer
			const calculationWithBuffer: IWorkingDaysCalculation = {
				...baseCalculation,
				workingDays: totalDaysWithBuffer,
			};
			
			const resultWithBuffer = calculationService.calculateWorkingDays(calculationWithBuffer);
			
			return {
				bufferPercentage,
				bufferDays,
				totalWorkingDays: totalDaysWithBuffer,
				endDate: resultWithBuffer.endDate,
				confidenceLevel: this.getConfidenceLevel(bufferPercentage),
				riskLevel: this.getRiskLevel(bufferPercentage),
			};
		});

		// Análise de riscos
		const riskFactors = this.analyzeRiskFactors(baseCalculation, baseResult);
		
		// Recomendações
		const recommendations = this.generateEstimateRecommendations(estimates, riskFactors);

		return {
			baseEstimate: {
				workingDays: baseResult.workingDaysCount,
				endDate: baseResult.endDate,
				confidence: "Baixa",
				risk: "Alto",
			},
			estimates,
			riskFactors,
			recommendations,
			summary: {
				recommendedBuffer: this.getRecommendedBuffer(riskFactors),
				mostLikelyScenario: estimates.find(e => e.bufferPercentage === 20) || estimates[0],
				conservativeScenario: estimates[estimates.length - 1],
			},
		};
	}

	/**
	 * Calcula estimativas baseadas em template de projeto
	 */
	calculateTemplateEstimate(template: IProjectTemplate, startDate: Date): IEstimateAnalysis {
		const baseCalculation: IWorkingDaysCalculation = {
			startDate,
			workingDays: template.defaultWorkingDays,
			includeStartDate: true,
			excludeHolidays: template.excludeHolidays,
			customWorkingDays: template.workingDaysPattern,
		};

		// Usar buffer do template como base
		const templateBufferPercentage = (template.estimatedBufferDays / template.defaultWorkingDays) * 100;
		const bufferPercentages = [
			Math.max(5, templateBufferPercentage - 10),
			templateBufferPercentage,
			templateBufferPercentage + 10,
		];

		return this.generateEstimates(baseCalculation, bufferPercentages);
	}

	// Métodos auxiliares privados

	private calculateMedian(numbers: number[]): number {
		const sorted = [...numbers].sort((a, b) => a - b);
		const middle = Math.floor(sorted.length / 2);
		
		if (sorted.length % 2 === 0) {
			return (sorted[middle - 1] + sorted[middle]) / 2;
		}
		return sorted[middle];
	}

	private calculateVariability(values: number[]): string {
		if (values.length < 2) return "Insuficiente";
		
		const average = values.reduce((sum, val) => sum + val, 0) / values.length;
		const variance = values.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / values.length;
		const coefficientOfVariation = Math.sqrt(variance) / average;
		
		if (coefficientOfVariation < 0.1) return "Baixa";
		if (coefficientOfVariation < 0.2) return "Moderada";
		return "Alta";
	}

	private getConfidenceLevel(bufferPercentage: number): string {
		if (bufferPercentage >= 30) return "Alta";
		if (bufferPercentage >= 20) return "Média";
		if (bufferPercentage >= 10) return "Baixa";
		return "Muito Baixa";
	}

	private getRiskLevel(bufferPercentage: number): string {
		if (bufferPercentage >= 30) return "Baixo";
		if (bufferPercentage >= 20) return "Médio";
		if (bufferPercentage >= 10) return "Alto";
		return "Muito Alto";
	}

	private analyzeRiskFactors(calculation: IWorkingDaysCalculation, result: IWorkingDaysResult): string[] {
		const risks: string[] = [];
		
		// Verificar se há muitos feriados no período
		if (result.holidaysInPeriod && result.holidaysInPeriod.length > 3) {
			risks.push("Período com muitos feriados");
		}
		
		// Verificar se o projeto é longo
		if (result.workingDaysCount > 60) {
			risks.push("Projeto de longa duração");
		}
		
		// Verificar padrão de trabalho não convencional
		if (calculation.customWorkingDays && calculation.customWorkingDays.length < 5) {
			risks.push("Padrão de trabalho reduzido");
		}
		
		// Verificar se inclui períodos de férias típicos
		const month = result.endDate.getMonth();
		if (month === 11 || month === 0 || month === 6) { // Dezembro, Janeiro, Julho
			risks.push("Período inclui época de férias");
		}
		
		return risks;
	}

	private generateScenarioRecommendations(results: any[]): string[] {
		const recommendations: string[] = [];
		
		if (results.length > 1) {
			const range = Math.max(...results.map(r => r.result.workingDaysCount)) - 
						 Math.min(...results.map(r => r.result.workingDaysCount));
			
			if (range > 10) {
				recommendations.push("Grande variação entre cenários - considere fatores de risco");
			}
			
			if (range < 3) {
				recommendations.push("Cenários similares - estimativa consistente");
			}
		}
		
		return recommendations;
	}

	private generateEstimateRecommendations(estimates: any[], riskFactors: string[]): string[] {
		const recommendations: string[] = [];
		
		if (riskFactors.length > 2) {
			recommendations.push("Múltiplos fatores de risco identificados - use estimativa conservadora");
		}
		
		if (riskFactors.includes("Projeto de longa duração")) {
			recommendations.push("Para projetos longos, considere revisões periódicas das estimativas");
		}
		
		if (riskFactors.includes("Período com muitos feriados")) {
			recommendations.push("Considere reagendar ou adicionar buffer extra para feriados");
		}
		
		return recommendations;
	}

	private getRecommendedBuffer(riskFactors: string[]): number {
		let baseBuffer = 15; // 15% base
		
		riskFactors.forEach(risk => {
			switch (risk) {
				case "Projeto de longa duração":
					baseBuffer += 5;
					break;
				case "Período com muitos feriados":
					baseBuffer += 10;
					break;
				case "Padrão de trabalho reduzido":
					baseBuffer += 5;
					break;
				case "Período inclui época de férias":
					baseBuffer += 10;
					break;
			}
		});
		
		return Math.min(baseBuffer, 40); // Máximo de 40%
	}
}

export const advancedCalculationService = new AdvancedCalculationService();

import { 
	IHistoryService, 
	ICalculationHistory, 
	IWorkingDaysCalculation, 
	IWorkingDaysResult,
	IUserPreferences,
	IHistoryFilter,
	IHistoryStats
} from "@/types";
import { storageService } from "./storageService";

class HistoryService implements IHistoryService {
	private readonly HISTORY_KEY = "working-days-history";
	private readonly PREFERENCES_KEY = "user-preferences";
	private readonly MAX_HISTORY_ITEMS = 1000; // Limite máximo de itens no histórico

	/**
	 * Adiciona um cálculo ao histórico
	 */
	async addCalculation(
		calculation: IWorkingDaysCalculation, 
		result: IWorkingDaysResult
	): Promise<ICalculationHistory> {
		const historyItem: ICalculationHistory = {
			id: this.generateId(),
			calculation,
			result,
			timestamp: new Date(),
			tags: this.generateAutoTags(calculation, result),
		};

		const history = await this.getHistory();
		
		// Adicionar no início da lista
		history.unshift(historyItem);
		
		// Manter apenas os últimos MAX_HISTORY_ITEMS
		if (history.length > this.MAX_HISTORY_ITEMS) {
			history.splice(this.MAX_HISTORY_ITEMS);
		}

		await storageService.setItem(this.HISTORY_KEY, history);
		return historyItem;
	}

	/**
	 * Obtém todo o histórico
	 */
	async getHistory(): Promise<ICalculationHistory[]> {
		const history = await storageService.getItem<ICalculationHistory[]>(this.HISTORY_KEY);
		
		if (!history) return [];
		
		// Converter strings de data de volta para objetos Date
		return history.map(item => ({
			...item,
			timestamp: new Date(item.timestamp),
			calculation: {
				...item.calculation,
				startDate: new Date(item.calculation.startDate),
			},
			result: {
				...item.result,
				endDate: new Date(item.result.endDate),
				holidaysInPeriod: item.result.holidaysInPeriod?.map(holiday => ({
					...holiday,
					date: new Date(holiday.date),
				})),
			},
		}));
	}

	/**
	 * Obtém histórico filtrado
	 */
	async getFilteredHistory(filter: IHistoryFilter): Promise<ICalculationHistory[]> {
		const history = await this.getHistory();
		
		return history.filter(item => {
			// Filtro por período
			if (filter.startDate && item.timestamp < filter.startDate) return false;
			if (filter.endDate && item.timestamp > filter.endDate) return false;
			
			// Filtro por range de dias úteis
			if (filter.minWorkingDays && item.result.workingDaysCount < filter.minWorkingDays) return false;
			if (filter.maxWorkingDays && item.result.workingDaysCount > filter.maxWorkingDays) return false;
			
			// Filtro por tags
			if (filter.tags && filter.tags.length > 0) {
				const hasMatchingTag = filter.tags.some(tag => 
					item.tags?.includes(tag)
				);
				if (!hasMatchingTag) return false;
			}
			
			// Filtro por texto (busca em tags e configurações)
			if (filter.searchText) {
				const searchLower = filter.searchText.toLowerCase();
				const matchesText = 
					item.tags?.some(tag => tag.toLowerCase().includes(searchLower)) ||
					item.result.workingDaysCount.toString().includes(searchLower);
				if (!matchesText) return false;
			}
			
			return true;
		});
	}

	/**
	 * Obtém um item específico do histórico
	 */
	async getHistoryItem(id: string): Promise<ICalculationHistory | null> {
		const history = await this.getHistory();
		return history.find(item => item.id === id) || null;
	}

	/**
	 * Remove um item do histórico
	 */
	async removeHistoryItem(id: string): Promise<boolean> {
		const history = await this.getHistory();
		const index = history.findIndex(item => item.id === id);
		
		if (index === -1) return false;
		
		history.splice(index, 1);
		await storageService.setItem(this.HISTORY_KEY, history);
		return true;
	}

	/**
	 * Limpa todo o histórico
	 */
	async clearHistory(): Promise<void> {
		await storageService.removeItem(this.HISTORY_KEY);
	}

	/**
	 * Atualiza tags de um item do histórico
	 */
	async updateHistoryItemTags(id: string, tags: string[]): Promise<boolean> {
		const history = await this.getHistory();
		const index = history.findIndex(item => item.id === id);
		
		if (index === -1) return false;
		
		history[index].tags = tags;
		await storageService.setItem(this.HISTORY_KEY, history);
		return true;
	}

	/**
	 * Obtém estatísticas do histórico
	 */
	async getHistoryStats(): Promise<IHistoryStats> {
		const history = await this.getHistory();
		
		if (history.length === 0) {
			return {
				totalCalculations: 0,
				totalWorkingDays: 0,
				averageWorkingDays: 0,
				mostUsedWorkingDays: 0,
				calculationsThisMonth: 0,
				calculationsThisWeek: 0,
				periodRange: null,
				topTags: [],
				projectSizeDistribution: {
					small: 0,
					medium: 0,
					large: 0,
				},
			};
		}

		const now = new Date();
		const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
		const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

		const workingDaysCounts = history.map(item => item.result.workingDaysCount);
		const totalWorkingDays = workingDaysCounts.reduce((sum, count) => sum + count, 0);
		
		// Contar frequência de dias úteis
		const workingDaysFrequency = workingDaysCounts.reduce((acc, count) => {
			acc[count] = (acc[count] || 0) + 1;
			return acc;
		}, {} as Record<number, number>);
		
		const mostUsedWorkingDays = parseInt(
			Object.entries(workingDaysFrequency)
				.sort(([,a], [,b]) => b - a)[0]?.[0] || "0"
		);

		// Contar tags
		const allTags = history.flatMap(item => item.tags || []);
		const tagFrequency = allTags.reduce((acc, tag) => {
			acc[tag] = (acc[tag] || 0) + 1;
			return acc;
		}, {} as Record<string, number>);
		
		const topTags = Object.entries(tagFrequency)
			.sort(([,a], [,b]) => b - a)
			.slice(0, 10)
			.map(([tag, count]) => ({ tag, count }));

		// Distribuição por tamanho de projeto
		const projectSizeDistribution = {
			small: history.filter(item => item.result.workingDaysCount <= 10).length,
			medium: history.filter(item => item.result.workingDaysCount > 10 && item.result.workingDaysCount <= 30).length,
			large: history.filter(item => item.result.workingDaysCount > 30).length,
		};

		// Range de período
		const timestamps = history.map(item => item.timestamp.getTime());
		const periodRange = {
			start: new Date(Math.min(...timestamps)),
			end: new Date(Math.max(...timestamps)),
		};

		return {
			totalCalculations: history.length,
			totalWorkingDays,
			averageWorkingDays: Math.round(totalWorkingDays / history.length),
			mostUsedWorkingDays,
			calculationsThisMonth: history.filter(item => item.timestamp >= thisMonth).length,
			calculationsThisWeek: history.filter(item => item.timestamp >= thisWeek).length,
			periodRange,
			topTags,
			projectSizeDistribution,
		};
	}

	/**
	 * Salva preferências do usuário
	 */
	async saveUserPreferences(preferences: IUserPreferences): Promise<void> {
		await storageService.setItem(this.PREFERENCES_KEY, preferences);
	}

	/**
	 * Obtém preferências do usuário
	 */
	async getUserPreferences(): Promise<IUserPreferences> {
		const preferences = await storageService.getItem<IUserPreferences>(this.PREFERENCES_KEY);
		
		// Retornar preferências padrão se não existirem
		return preferences || {
			defaultIncludeStartDate: true,
			defaultExcludeHolidays: true,
			defaultWorkingDays: [1, 2, 3, 4, 5], // Segunda a sexta
			autoSaveCalculations: true,
			maxHistoryItems: 100,
			defaultExportFormat: "csv",
			theme: "system",
			notifications: {
				showSuccess: true,
				showErrors: true,
				showWarnings: true,
			},
			dateFormat: "dd/MM/yyyy",
			firstDayOfWeek: 1, // Segunda-feira
		};
	}

	/**
	 * Exporta histórico para backup
	 */
	async exportHistory(): Promise<string> {
		const history = await this.getHistory();
		const preferences = await this.getUserPreferences();
		
		const backup = {
			version: "1.0",
			exportDate: new Date().toISOString(),
			history,
			preferences,
		};
		
		return JSON.stringify(backup, null, 2);
	}

	/**
	 * Importa histórico de backup
	 */
	async importHistory(backupData: string): Promise<boolean> {
		try {
			const backup = JSON.parse(backupData);
			
			if (!backup.history || !Array.isArray(backup.history)) {
				throw new Error("Formato de backup inválido");
			}
			
			// Validar e converter dados
			const history: ICalculationHistory[] = backup.history.map((item: any) => ({
				...item,
				timestamp: new Date(item.timestamp),
				calculation: {
					...item.calculation,
					startDate: new Date(item.calculation.startDate),
				},
				result: {
					...item.result,
					endDate: new Date(item.result.endDate),
					holidaysInPeriod: item.result.holidaysInPeriod?.map((holiday: any) => ({
						...holiday,
						date: new Date(holiday.date),
					})),
				},
			}));
			
			await storageService.setItem(this.HISTORY_KEY, history);
			
			// Importar preferências se existirem
			if (backup.preferences) {
				await this.saveUserPreferences(backup.preferences);
			}
			
			return true;
		} catch (error) {
			console.error("Erro ao importar histórico:", error);
			return false;
		}
	}

	/**
	 * Gera ID único para item do histórico
	 */
	private generateId(): string {
		return `calc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
	 * Gera tags automáticas baseadas no cálculo
	 */
	private generateAutoTags(
		calculation: IWorkingDaysCalculation, 
		result: IWorkingDaysResult
	): string[] {
		const tags: string[] = [];
		
		// Tag por tamanho do projeto
		if (result.workingDaysCount <= 10) {
			tags.push("projeto-pequeno");
		} else if (result.workingDaysCount <= 30) {
			tags.push("projeto-medio");
		} else {
			tags.push("projeto-grande");
		}
		
		// Tag por mês
		const month = calculation.startDate.toLocaleDateString('pt-BR', { month: 'long' });
		tags.push(`mes-${month}`);
		
		// Tag por ano
		tags.push(`ano-${calculation.startDate.getFullYear()}`);
		
		// Tag por configuração
		if (!calculation.includeStartDate) {
			tags.push("sem-data-inicial");
		}
		
		if (!calculation.excludeHolidays) {
			tags.push("com-feriados");
		}
		
		if (calculation.customWorkingDays && calculation.customWorkingDays.length !== 5) {
			tags.push("horario-customizado");
		}
		
		// Tag por quantidade de feriados
		const holidayCount = result.holidaysInPeriod?.length || 0;
		if (holidayCount > 0) {
			tags.push(`${holidayCount}-feriados`);
		}
		
		return tags;
	}
}

export const historyService = new HistoryService();

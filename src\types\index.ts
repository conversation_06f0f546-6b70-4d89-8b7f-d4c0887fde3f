// ============================================================================
// TIPOS PRINCIPAIS DO SISTEMA
// ============================================================================

// Tipos base para datas e períodos
export interface IDateRange {
	startDate: Date;
	endDate: Date;
}

export interface IPeriodStats {
	totalDays: number;
	workingDays: number;
	weekends: number;
	holidays: number;
	holidaysList: IHoliday[];
}

// ============================================================================
// SISTEMA DE FERIADOS
// ============================================================================

export interface IHoliday {
	date: string; // formato YYYY-MM-DD
	name: string;
	type: "nacional" | "estadual" | "municipal" | "personalizado";
	description?: string;
	isRecurring?: boolean;
	region?: string;
}

export interface IHolidayConfiguration {
	enabledTypes: IHoliday["type"][];
	customHolidays: IHoliday[];
	region: string;
	includeRegionalHolidays: boolean;
}

export interface IHolidayService {
	getHolidaysForYear: (year: number) => IHoliday[];
	getHolidaysForPeriod: (startDate: Date, endDate: Date) => IHoliday[];
	isHoliday: (date: Date) => boolean;
	getHolidayName: (date: Date) => string | null;
	addCustomHoliday: (holiday: Omit<IHoliday, "type">) => void;
	removeCustomHoliday: (date: string) => void;
	updateConfiguration: (config: Partial<IHolidayConfiguration>) => void;
	getConfiguration: () => IHolidayConfiguration;
}

// ============================================================================
// SISTEMA DE CÁLCULOS
// ============================================================================

export interface IWorkingDaysCalculation {
	startDate: Date;
	workingDays: number;
	endDate?: Date;
	includeStartDate: boolean;
	excludeHolidays: boolean;
	customWorkingDays?: number[]; // 0-6, onde 0 = domingo
}

export interface ICalculationResult {
	endDate: Date;
	periodStats: IPeriodStats;
	calculationDetails: {
		totalDaysAdded: number;
		weekendsSkipped: number;
		holidaysSkipped: number;
		actualWorkingDays: number;
	};
}

export interface ICalculationService {
	calculateEndDate: (calculation: IWorkingDaysCalculation) => ICalculationResult;
	calculateWorkingDaysBetween: (startDate: Date, endDate: Date) => IPeriodStats;
	calculateBusinessDays: (startDate: Date, days: number, options?: Partial<IWorkingDaysCalculation>) => Date;
}

// ============================================================================
// SISTEMA DE TEMPLATES
// ============================================================================

export interface IProjectTemplate {
	id: string;
	name: string;
	description: string;
	category: "desenvolvimento" | "marketing" | "design" | "consultoria" | "personalizado";
	defaultWorkingDays: number;
	workingDaysPattern: number[]; // dias da semana considerados úteis
	excludeHolidays: boolean;
	estimatedBufferDays: number;
	phases?: IProjectPhase[];
	createdAt: Date;
	updatedAt: Date;
}

export interface IProjectPhase {
	id: string;
	name: string;
	description: string;
	estimatedDays: number;
	dependencies: string[]; // IDs de outras fases
	isOptional: boolean;
}

export interface ITemplateService {
	getTemplates: () => IProjectTemplate[];
	getTemplateById: (id: string) => IProjectTemplate | null;
	createTemplate: (template: Omit<IProjectTemplate, "id" | "createdAt" | "updatedAt">) => IProjectTemplate;
	updateTemplate: (id: string, updates: Partial<IProjectTemplate>) => IProjectTemplate | null;
	deleteTemplate: (id: string) => boolean;
	getDefaultTemplates: () => IProjectTemplate[];
}

// ============================================================================
// SISTEMA DE PERSISTÊNCIA LOCAL
// ============================================================================

export interface IUserPreferences {
	theme: "light" | "dark" | "system";
	language: "pt-BR" | "en-US";
	dateFormat: "dd/MM/yyyy" | "MM/dd/yyyy" | "yyyy-MM-dd";
	defaultWorkingDays: number[];
	defaultExcludeHolidays: boolean;
	defaultIncludeStartDate: boolean;
	autoSaveCalculations: boolean;
	maxHistoryItems: number;
	defaultExportFormat: "csv" | "excel" | "pdf" | "json";
	firstDayOfWeek: number;
	notifications: {
		showSuccess: boolean;
		showErrors: boolean;
		showWarnings: boolean;
	};
}

export interface ICalculationHistory {
	id: string;
	timestamp: Date;
	calculation: IWorkingDaysCalculation;
	result: IWorkingDaysResult;
	templateUsed?: string;
	notes?: string;
	tags?: string[];
}

// Interface para resultado de cálculo de dias úteis
export interface IWorkingDaysResult {
	workingDaysCount: number;
	endDate: Date;
	totalDays?: number;
	weekendsCount?: number;
	holidaysInPeriod?: IHoliday[];
	calculationDetails?: {
		totalDaysAdded: number;
		weekendsSkipped: number;
		holidaysSkipped: number;
		actualWorkingDays: number;
	};
}

// Interface para filtros de histórico
export interface IHistoryFilter {
	searchText?: string;
	tags?: string[];
	dateRange?: IDateRange;
	workingDaysRange?: {
		min: number;
		max: number;
	};
	templateUsed?: string;
}

// Interface para estatísticas do histórico
export interface IHistoryStats {
	totalCalculations: number;
	totalWorkingDays: number;
	averageWorkingDays: number;
	mostUsedWorkingDays: number;
	calculationsThisMonth: number;
	calculationsThisWeek: number;
	periodRange: {
		start: Date;
		end: Date;
	} | null;
	topTags: Array<{
		tag: string;
		count: number;
	}>;
	projectSizeDistribution: {
		small: number; // ≤10 dias
		medium: number; // 11-30 dias
		large: number; // >30 dias
	};
}

// Interface para o serviço de histórico
export interface IHistoryService {
	addCalculation: (calculation: IWorkingDaysCalculation, result: IWorkingDaysResult) => Promise<ICalculationHistory>;
	getHistory: () => Promise<ICalculationHistory[]>;
	getFilteredHistory: (filter: IHistoryFilter) => Promise<ICalculationHistory[]>;
	getHistoryItem: (id: string) => Promise<ICalculationHistory | null>;
	removeHistoryItem: (id: string) => Promise<boolean>;
	clearHistory: () => Promise<void>;
	updateHistoryItemTags: (id: string, tags: string[]) => Promise<boolean>;
	getHistoryStats: () => Promise<IHistoryStats>;
	getUserPreferences: () => Promise<IUserPreferences>;
	saveUserPreferences: (preferences: IUserPreferences) => Promise<void>;
	exportHistory: () => Promise<string>;
	importHistory: (data: string) => Promise<boolean>;
}

// Interface para o hook de histórico
export interface IHistoryHook {
	history: ICalculationHistory[];
	filteredHistory: ICalculationHistory[];
	stats: IHistoryStats | null;
	preferences: IUserPreferences | null;
	isLoading: boolean;
	error: string | null;
	currentFilter: IHistoryFilter;
	addCalculation: (calculation: IWorkingDaysCalculation, result: IWorkingDaysResult) => Promise<ICalculationHistory>;
	removeHistoryItem: (id: string) => Promise<boolean>;
	clearHistory: () => Promise<void>;
	updateItemTags: (id: string, tags: string[]) => Promise<boolean>;
	applyFilter: (filter: IHistoryFilter) => Promise<void>;
	clearFilter: () => void;
	savePreferences: (preferences: IUserPreferences) => Promise<void>;
	exportHistory: () => Promise<string>;
	importHistory: (data: string) => Promise<boolean>;
	getHistoryItem: (id: string) => Promise<ICalculationHistory | null>;
	refreshHistory: () => Promise<void>;
	refreshStats: () => Promise<void>;
}

export interface IStorageService {
	// Preferências
	getPreferences: () => IUserPreferences;
	updatePreferences: (preferences: Partial<IUserPreferences>) => void;

	// Histórico
	getHistory: () => ICalculationHistory[];
	addToHistory: (item: Omit<ICalculationHistory, "id" | "timestamp">) => void;
	removeFromHistory: (id: string) => void;
	clearHistory: () => void;

	// Templates personalizados
	getCustomTemplates: () => IProjectTemplate[];
	saveCustomTemplate: (template: IProjectTemplate) => void;
	removeCustomTemplate: (id: string) => void;

	// Feriados personalizados
	getCustomHolidays: () => IHoliday[];
	saveCustomHolidays: (holidays: IHoliday[]) => void;

	// Configurações de feriados
	getHolidayConfiguration: () => IHolidayConfiguration;
	saveHolidayConfiguration: (config: IHolidayConfiguration) => void;
}

// ============================================================================
// HOOKS E COMPONENTES
// ============================================================================

export interface IWorkingDaysCalculatorHook {
	// Estado atual
	calculation: IWorkingDaysCalculation;
	result: ICalculationResult | null;
	isCalculating: boolean;
	error: string | null;

	// Ações
	updateCalculation: (updates: Partial<IWorkingDaysCalculation>) => void;
	calculate: () => Promise<void>;
	reset: () => void;
	saveToHistory: (notes?: string) => void;

	// Dados derivados
	periodStats: IPeriodStats | null;
	canCalculate: boolean;
}

export interface IHolidayManagerHook {
	holidays: IHoliday[];
	configuration: IHolidayConfiguration;
	isLoading: boolean;
	error: string | null;

	addCustomHoliday: (holiday: Omit<IHoliday, "type">) => void;
	removeCustomHoliday: (date: string) => void;
	updateConfiguration: (config: Partial<IHolidayConfiguration>) => void;
	getHolidaysForYear: (year: number) => IHoliday[];
	isHoliday: (date: Date) => boolean;
}

export interface ITemplateManagerHook {
	templates: IProjectTemplate[];
	selectedTemplate: IProjectTemplate | null;
	isLoading: boolean;
	error: string | null;

	selectTemplate: (id: string) => void;
	createTemplate: (template: Omit<IProjectTemplate, "id" | "createdAt" | "updatedAt">) => void;
	updateTemplate: (id: string, updates: Partial<IProjectTemplate>) => void;
	deleteTemplate: (id: string) => void;
	applyTemplate: (templateId: string) => IWorkingDaysCalculation;
}

// ============================================================================
// TIPOS DE EXPORTAÇÃO E RELATÓRIOS
// ============================================================================

export interface IExportOptions {
	format: "pdf" | "excel" | "csv" | "ical" | "json";
	includeDetails: boolean;
	includeChart: boolean;
	dateRange?: IDateRange;
	templateInfo?: boolean;
}

export interface IReportData {
	calculation: IWorkingDaysCalculation;
	result: ICalculationResult;
	template?: IProjectTemplate;
	exportOptions: IExportOptions;
	generatedAt: Date;
}

export interface IExportService {
	exportToPDF: (data: IReportData) => Promise<Blob>;
	exportToExcel: (data: IReportData) => Promise<Blob>;
	exportToCSV: (data: IReportData) => string;
	exportToICal: (data: IReportData) => string;
	exportToJSON: (data: IReportData) => string;
}

// ============================================================================
// TIPOS DE NOTIFICAÇÃO E FEEDBACK
// ============================================================================

export interface INotification {
	id: string;
	type: "success" | "error" | "warning" | "info";
	title: string;
	message: string;
	duration?: number;
	action?: {
		label: string;
		onClick: () => void;
	};
}

export interface IValidationError {
	field: string;
	message: string;
	type: "required" | "invalid" | "range" | "custom";
}

export interface IFormValidation {
	isValid: boolean;
	errors: IValidationError[];
	warnings: IValidationError[];
}

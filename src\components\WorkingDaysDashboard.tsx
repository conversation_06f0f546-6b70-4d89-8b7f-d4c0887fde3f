"use client";

import React, { useMemo } from "react";
import { format, differenceInDays, startOfYear, endOfYear, eachMonthOfInterval } from "date-fns";
import { ptBR } from "date-fns/locale";
import { 
	Calendar, 
	Clock, 
	TrendingUp, 
	BarChart3, 
	PieChart, 
	Target,
	AlertCircle,
	CheckCircle
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { IWorkingDaysCalculation, IWorkingDaysResult, ICalculationHistory } from "@/types";

interface IWorkingDaysDashboardProps {
	calculation?: IWorkingDaysCalculation;
	result?: IWorkingDaysResult;
	history?: ICalculationHistory[];
	showAnalytics?: boolean;
}

export function WorkingDaysDashboard({
	calculation,
	result,
	history = [],
	showAnalytics = true
}: IWorkingDaysDashboardProps) {

	// Métricas principais
	const mainMetrics = useMemo(() => {
		if (!calculation || !result) return null;

		const totalDays = differenceInDays(result.endDate, calculation.startDate) + 1;
		const workingDaysPercentage = (result.workingDaysCount / totalDays) * 100;
		const excludedDays = totalDays - result.workingDaysCount;

		return {
			totalDays,
			workingDays: result.workingDaysCount,
			excludedDays,
			workingDaysPercentage,
			holidaysCount: result.holidaysInPeriod?.length || 0,
			weekendsCount: excludedDays - (result.holidaysInPeriod?.length || 0),
		};
	}, [calculation, result]);

	// Análise do histórico
	const historyAnalytics = useMemo(() => {
		if (!showAnalytics || history.length === 0) return null;

		const recentHistory = history.slice(0, 10);
		const avgWorkingDays = recentHistory.reduce((sum, item) => sum + item.result.workingDaysCount, 0) / recentHistory.length;
		
		const projectTypes = recentHistory.reduce((acc, item) => {
			const days = item.result.workingDaysCount;
			let type = "Pequeno";
			if (days > 30) type = "Grande";
			else if (days > 10) type = "Médio";
			
			acc[type] = (acc[type] || 0) + 1;
			return acc;
		}, {} as Record<string, number>);

		const monthlyUsage = recentHistory.reduce((acc, item) => {
			const month = format(item.timestamp, "MMM", { locale: ptBR });
			acc[month] = (acc[month] || 0) + 1;
			return acc;
		}, {} as Record<string, number>);

		return {
			totalCalculations: history.length,
			avgWorkingDays: Math.round(avgWorkingDays),
			projectTypes,
			monthlyUsage,
			recentHistory,
		};
	}, [history, showAnalytics]);

	// Progresso do ano
	const yearProgress = useMemo(() => {
		const now = new Date();
		const yearStart = startOfYear(now);
		const yearEnd = endOfYear(now);
		const totalDaysInYear = differenceInDays(yearEnd, yearStart) + 1;
		const daysPassed = differenceInDays(now, yearStart) + 1;
		const progressPercentage = (daysPassed / totalDaysInYear) * 100;

		return {
			daysPassed,
			totalDaysInYear,
			progressPercentage,
			daysRemaining: totalDaysInYear - daysPassed,
		};
	}, []);

	return (
		<div className="space-y-6">
			{/* Métricas principais */}
			{mainMetrics && (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Total de Dias</CardTitle>
							<Calendar className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{mainMetrics.totalDays}</div>
							<p className="text-xs text-muted-foreground">
								Do período selecionado
							</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Dias Úteis</CardTitle>
							<Clock className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-blue-600">{mainMetrics.workingDays}</div>
							<p className="text-xs text-muted-foreground">
								{mainMetrics.workingDaysPercentage.toFixed(1)}% do período
							</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Feriados</CardTitle>
							<AlertCircle className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-red-600">{mainMetrics.holidaysCount}</div>
							<p className="text-xs text-muted-foreground">
								Feriados no período
							</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Fins de Semana</CardTitle>
							<Target className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-gray-600">{mainMetrics.weekendsCount}</div>
							<p className="text-xs text-muted-foreground">
								Dias não úteis
							</p>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Progresso visual */}
			{mainMetrics && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<BarChart3 className="h-5 w-5" />
							Distribuição do Período
						</CardTitle>
						<CardDescription>
							Visualização da composição dos dias no período calculado
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="space-y-3">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">Dias Úteis</span>
								<span className="text-sm text-muted-foreground">
									{mainMetrics.workingDays} dias ({mainMetrics.workingDaysPercentage.toFixed(1)}%)
								</span>
							</div>
							<Progress value={mainMetrics.workingDaysPercentage} className="h-2" />
						</div>

						<div className="grid grid-cols-3 gap-4 mt-4">
							<div className="text-center p-3 bg-blue-50 rounded-lg">
								<div className="text-lg font-bold text-blue-600">{mainMetrics.workingDays}</div>
								<div className="text-xs text-blue-700">Dias Úteis</div>
							</div>
							<div className="text-center p-3 bg-red-50 rounded-lg">
								<div className="text-lg font-bold text-red-600">{mainMetrics.holidaysCount}</div>
								<div className="text-xs text-red-700">Feriados</div>
							</div>
							<div className="text-center p-3 bg-gray-50 rounded-lg">
								<div className="text-lg font-bold text-gray-600">{mainMetrics.weekendsCount}</div>
								<div className="text-xs text-gray-700">Fins de Semana</div>
							</div>
						</div>
					</CardContent>
				</Card>
			)}

			{/* Progresso do ano */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<TrendingUp className="h-5 w-5" />
						Progresso do Ano {new Date().getFullYear()}
					</CardTitle>
					<CardDescription>
						Acompanhe o progresso do ano atual
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="space-y-2">
						<div className="flex items-center justify-between">
							<span className="text-sm font-medium">Progresso Anual</span>
							<span className="text-sm text-muted-foreground">
								{yearProgress.daysPassed} de {yearProgress.totalDaysInYear} dias
							</span>
						</div>
						<Progress value={yearProgress.progressPercentage} className="h-3" />
						<div className="text-xs text-muted-foreground text-center">
							{yearProgress.progressPercentage.toFixed(1)}% do ano concluído • {yearProgress.daysRemaining} dias restantes
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Analytics do histórico */}
			{historyAnalytics && (
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<PieChart className="h-5 w-5" />
								Estatísticas de Uso
							</CardTitle>
							<CardDescription>
								Análise dos seus cálculos recentes
							</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-2 gap-4">
								<div className="text-center">
									<div className="text-2xl font-bold">{historyAnalytics.totalCalculations}</div>
									<div className="text-sm text-muted-foreground">Total de cálculos</div>
								</div>
								<div className="text-center">
									<div className="text-2xl font-bold text-blue-600">{historyAnalytics.avgWorkingDays}</div>
									<div className="text-sm text-muted-foreground">Média de dias úteis</div>
								</div>
							</div>

							<div className="space-y-3">
								<div className="text-sm font-medium">Tipos de Projeto</div>
								{Object.entries(historyAnalytics.projectTypes).map(([type, count]) => (
									<div key={type} className="flex items-center justify-between">
										<span className="text-sm">{type}</span>
										<Badge variant="outline">{count}</Badge>
									</div>
								))}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<CheckCircle className="h-5 w-5" />
								Cálculos Recentes
							</CardTitle>
							<CardDescription>
								Seus últimos cálculos realizados
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{historyAnalytics.recentHistory.slice(0, 5).map((item, index) => (
									<div key={item.id} className="flex items-center justify-between p-2 rounded-lg border">
										<div className="flex-1">
											<div className="text-sm font-medium">
												{item.result.workingDaysCount} dias úteis
											</div>
											<div className="text-xs text-muted-foreground">
												{format(item.timestamp, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
											</div>
										</div>
										<Badge variant={index === 0 ? "default" : "secondary"}>
											{index === 0 ? "Mais recente" : `${index + 1}º`}
										</Badge>
									</div>
								))}
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Informações detalhadas do cálculo atual */}
			{calculation && result && (
				<Card>
					<CardHeader>
						<CardTitle>Detalhes do Cálculo Atual</CardTitle>
						<CardDescription>
							Informações completas sobre o período calculado
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							<div className="space-y-2">
								<div className="text-sm font-medium text-muted-foreground">Período</div>
								<div className="space-y-1">
									<div className="text-sm">
										<span className="font-medium">Início:</span> {format(calculation.startDate, "dd/MM/yyyy", { locale: ptBR })}
									</div>
									<div className="text-sm">
										<span className="font-medium">Fim:</span> {format(result.endDate, "dd/MM/yyyy", { locale: ptBR })}
									</div>
								</div>
							</div>

							<div className="space-y-2">
								<div className="text-sm font-medium text-muted-foreground">Configurações</div>
								<div className="space-y-1">
									<div className="text-sm">
										<span className="font-medium">Incluir data inicial:</span> {calculation.includeStartDate ? "Sim" : "Não"}
									</div>
									<div className="text-sm">
										<span className="font-medium">Excluir feriados:</span> {calculation.excludeHolidays ? "Sim" : "Não"}
									</div>
								</div>
							</div>

							<div className="space-y-2">
								<div className="text-sm font-medium text-muted-foreground">Dias de Trabalho</div>
								<div className="flex flex-wrap gap-1">
									{["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"].map((day, index) => (
										<Badge 
											key={day} 
											variant={calculation.customWorkingDays?.includes(index) ? "default" : "outline"}
											className="text-xs"
										>
											{day}
										</Badge>
									))}
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			)}
		</div>
	);
}

import { useState, useCallback, useEffect, useMemo } from "react";
import { 
	IHolidayManagerHook, 
	IHoliday, 
	IHolidayConfiguration 
} from "@/types";
import { holidayService } from "@/lib/holidays";
import { storageService } from "@/services/storageService";
import { useNotifications } from "./useNotifications";

export function useHolidayManager(): IHolidayManagerHook {
	const [holidays, setHolidays] = useState<IHoliday[]>([]);
	const [configuration, setConfiguration] = useState<IHolidayConfiguration>(
		holidayService.getConfiguration()
	);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	
	const { addNotification } = useNotifications();

	// Carregar dados iniciais
	useEffect(() => {
		loadInitialData();
	}, []);

	const loadInitialData = useCallback(async () => {
		setIsLoading(true);
		setError(null);

		try {
			// Carregar configuração salva
			const savedConfig = storageService.getHolidayConfiguration();
			holidayService.updateConfiguration(savedConfig);
			setConfiguration(savedConfig);

			// Carregar feriados personalizados
			const customHolidays = storageService.getCustomHolidays();
			customHolidays.forEach(holiday => {
				holidayService.addCustomHoliday(holiday);
			});

			// Carregar feriados do ano atual
			const currentYear = new Date().getFullYear();
			const yearHolidays = holidayService.getHolidaysForYear(currentYear);
			setHolidays(yearHolidays);

		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao carregar feriados";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro ao carregar feriados",
				message: errorMessage,
				duration: 5000,
			});
		} finally {
			setIsLoading(false);
		}
	}, [addNotification]);

	// Adicionar feriado personalizado
	const addCustomHoliday = useCallback((holiday: Omit<IHoliday, "type">) => {
		try {
			holidayService.addCustomHoliday(holiday);
			
			// Salvar no localStorage
			const allCustomHolidays = storageService.getCustomHolidays();
			const newHoliday: IHoliday = { ...holiday, type: "personalizado" };
			const updatedHolidays = allCustomHolidays.filter(h => h.date !== holiday.date);
			updatedHolidays.push(newHoliday);
			storageService.saveCustomHolidays(updatedHolidays);

			// Atualizar lista de feriados
			const currentYear = new Date().getFullYear();
			const yearHolidays = holidayService.getHolidaysForYear(currentYear);
			setHolidays(yearHolidays);

			addNotification({
				type: "success",
				title: "Feriado adicionado",
				message: `${holiday.name} foi adicionado com sucesso`,
				duration: 3000,
			});

		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao adicionar feriado";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro ao adicionar feriado",
				message: errorMessage,
				duration: 5000,
			});
		}
	}, [addNotification]);

	// Remover feriado personalizado
	const removeCustomHoliday = useCallback((date: string) => {
		try {
			holidayService.removeCustomHoliday(date);
			
			// Remover do localStorage
			const allCustomHolidays = storageService.getCustomHolidays();
			const updatedHolidays = allCustomHolidays.filter(h => h.date !== date);
			storageService.saveCustomHolidays(updatedHolidays);

			// Atualizar lista de feriados
			const currentYear = new Date().getFullYear();
			const yearHolidays = holidayService.getHolidaysForYear(currentYear);
			setHolidays(yearHolidays);

			addNotification({
				type: "success",
				title: "Feriado removido",
				message: "Feriado personalizado foi removido",
				duration: 3000,
			});

		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao remover feriado";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro ao remover feriado",
				message: errorMessage,
				duration: 5000,
			});
		}
	}, [addNotification]);

	// Atualizar configuração
	const updateConfiguration = useCallback((config: Partial<IHolidayConfiguration>) => {
		try {
			const newConfig = { ...configuration, ...config };
			
			holidayService.updateConfiguration(newConfig);
			storageService.saveHolidayConfiguration(newConfig);
			setConfiguration(newConfig);

			// Recarregar feriados com nova configuração
			const currentYear = new Date().getFullYear();
			const yearHolidays = holidayService.getHolidaysForYear(currentYear);
			setHolidays(yearHolidays);

			addNotification({
				type: "success",
				title: "Configuração atualizada",
				message: "Configurações de feriados foram salvas",
				duration: 3000,
			});

		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao atualizar configuração";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro na configuração",
				message: errorMessage,
				duration: 5000,
			});
		}
	}, [configuration, addNotification]);

	// Obter feriados de um ano específico
	const getHolidaysForYear = useCallback((year: number) => {
		return holidayService.getHolidaysForYear(year);
	}, []);

	// Verificar se uma data é feriado
	const isHoliday = useCallback((date: Date) => {
		return holidayService.isHoliday(date);
	}, []);

	return {
		holidays,
		configuration,
		isLoading,
		error,
		addCustomHoliday,
		removeCustomHoliday,
		updateConfiguration,
		getHolidaysForYear,
		isHoliday,
	};
}

// Hook para gerenciar feriados regionais
export function useRegionalHolidays() {
	const [selectedRegion, setSelectedRegion] = useState("BR");
	const [availableRegions] = useState([
		{ code: "BR", name: "Brasil", type: "país" },
		{ code: "SP", name: "São Paulo", type: "estado" },
		{ code: "RJ", name: "Rio de Janeiro", type: "estado" },
		{ code: "MG", name: "Minas Gerais", type: "estado" },
		{ code: "RS", name: "Rio Grande do Sul", type: "estado" },
		// Adicionar mais regiões conforme necessário
	]);

	const getRegionalHolidays = useCallback((region: string, year: number) => {
		// Implementar lógica para feriados regionais específicos
		// Por enquanto, retorna array vazio
		return [];
	}, []);

	const updateRegion = useCallback((region: string) => {
		setSelectedRegion(region);
		// Atualizar configuração de feriados
	}, []);

	return {
		selectedRegion,
		availableRegions,
		getRegionalHolidays,
		updateRegion,
	};
}

// Hook para importação/exportação de feriados
export function useHolidayImportExport() {
	const { addNotification } = useNotifications();

	const exportHolidays = useCallback((holidays: IHoliday[], format: "json" | "csv" | "ical") => {
		try {
			let content = "";
			let filename = "";
			let mimeType = "";

			switch (format) {
				case "json":
					content = JSON.stringify(holidays, null, 2);
					filename = `feriados_${new Date().toISOString().split('T')[0]}.json`;
					mimeType = "application/json";
					break;

				case "csv":
					const csvHeader = "Data,Nome,Tipo,Descrição\n";
					const csvRows = holidays.map(h => 
						`${h.date},"${h.name}","${h.type}","${h.description || ""}"`
					).join("\n");
					content = csvHeader + csvRows;
					filename = `feriados_${new Date().toISOString().split('T')[0]}.csv`;
					mimeType = "text/csv";
					break;

				case "ical":
					const icalHeader = "BEGIN:VCALENDAR\nVERSION:2.0\nPRODID:-//Working Days Counter//Holidays//PT\n";
					const icalFooter = "END:VCALENDAR";
					const icalEvents = holidays.map(h => {
						const date = h.date.replace(/-/g, "");
						return `BEGIN:VEVENT\nDTSTART;VALUE=DATE:${date}\nSUMMARY:${h.name}\nDESCRIPTION:${h.description || h.name}\nEND:VEVENT`;
					}).join("\n");
					content = icalHeader + icalEvents + "\n" + icalFooter;
					filename = `feriados_${new Date().toISOString().split('T')[0]}.ics`;
					mimeType = "text/calendar";
					break;
			}

			// Criar e baixar arquivo
			const blob = new Blob([content], { type: mimeType });
			const url = URL.createObjectURL(blob);
			const link = document.createElement("a");
			link.href = url;
			link.download = filename;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
			URL.revokeObjectURL(url);

			addNotification({
				type: "success",
				title: "Exportação concluída",
				message: `Feriados exportados em formato ${format.toUpperCase()}`,
				duration: 3000,
			});

		} catch (err) {
			addNotification({
				type: "error",
				title: "Erro na exportação",
				message: "Não foi possível exportar os feriados",
				duration: 5000,
			});
		}
	}, [addNotification]);

	const importHolidays = useCallback((file: File): Promise<IHoliday[]> => {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			
			reader.onload = (e) => {
				try {
					const content = e.target?.result as string;
					let holidays: IHoliday[] = [];

					if (file.name.endsWith('.json')) {
						holidays = JSON.parse(content);
					} else if (file.name.endsWith('.csv')) {
						const lines = content.split('\n').slice(1); // Pular cabeçalho
						holidays = lines.map(line => {
							const [date, name, type, description] = line.split(',').map(s => s.replace(/"/g, ''));
							return {
								date,
								name,
								type: type as IHoliday["type"],
								description,
							};
						}).filter(h => h.date && h.name);
					}

					addNotification({
						type: "success",
						title: "Importação concluída",
						message: `${holidays.length} feriados importados`,
						duration: 3000,
					});

					resolve(holidays);
				} catch (err) {
					addNotification({
						type: "error",
						title: "Erro na importação",
						message: "Formato de arquivo inválido",
						duration: 5000,
					});
					reject(err);
				}
			};

			reader.readAsText(file);
		});
	}, [addNotification]);

	return {
		exportHolidays,
		importHolidays,
	};
}

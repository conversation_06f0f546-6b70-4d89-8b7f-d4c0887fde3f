"use client";

import React, { useState } from "react";
import { 
	Folder, 
	Plus, 
	Edit, 
	Trash2, 
	Copy, 
	Download, 
	Upload,
	Clock,
	CheckCircle,
	AlertCircle,
	BarChart3
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { useTemplateManager, useTemplateAnalysis, useTemplateCustomization } from "@/hooks/useTemplateManager";
import { IProjectTemplate } from "@/types";

interface ITemplateManagerProps {
	onTemplateSelect?: (template: IProjectTemplate) => void;
	compact?: boolean;
}

export function TemplateManager({ onTemplateSelect, compact = false }: ITemplateManagerProps) {
	const {
		templates,
		selectedTemplate,
		isLoading,
		error,
		selectTemplate,
		deleteTemplate,
		applyTemplate,
	} = useTemplateManager();

	const { calculateTemplateMetrics } = useTemplateAnalysis();
	const { cloneTemplate, exportTemplate, importTemplate } = useTemplateCustomization();

	const [filterCategory, setFilterCategory] = useState<string>("all");
	const [searchTerm, setSearchTerm] = useState("");
	const [showCreateForm, setShowCreateForm] = useState(false);

	// Filtrar templates
	const filteredTemplates = React.useMemo(() => {
		let filtered = templates;

		if (filterCategory !== "all") {
			filtered = filtered.filter(t => t.category === filterCategory);
		}

		if (searchTerm) {
			filtered = filtered.filter(t => 
				t.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				t.description.toLowerCase().includes(searchTerm.toLowerCase())
			);
		}

		return filtered.sort((a, b) => a.name.localeCompare(b.name));
	}, [templates, filterCategory, searchTerm]);

	// Obter categorias únicas
	const categories = React.useMemo(() => {
		const cats = Array.from(new Set(templates.map(t => t.category)));
		return cats.sort();
	}, [templates]);

	// Aplicar template
	const handleApplyTemplate = (template: IProjectTemplate) => {
		try {
			const calculation = applyTemplate(template.id);
			onTemplateSelect?.(template);
		} catch (error) {
			console.error("Erro ao aplicar template:", error);
		}
	};

	// Clonar template
	const handleCloneTemplate = (template: IProjectTemplate) => {
		const newName = `${template.name} (Cópia)`;
		cloneTemplate(template.id, newName);
	};

	// Importar template
	const handleImportTemplate = async (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (!file) return;

		try {
			await importTemplate(file);
		} catch (error) {
			console.error("Erro na importação:", error);
		}

		// Limpar input
		event.target.value = "";
	};

	if (compact) {
		return (
			<Card className="w-full">
				<CardHeader className="pb-3">
					<CardTitle className="text-lg flex items-center gap-2">
						<Folder className="h-5 w-5" />
						Templates de Projeto
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-2 max-h-60 overflow-y-auto">
						{filteredTemplates.map((template) => {
							const metrics = calculateTemplateMetrics(template);
							return (
								<div
									key={template.id}
									className="flex items-center justify-between p-3 rounded-lg border hover:bg-accent cursor-pointer"
									onClick={() => handleApplyTemplate(template)}
								>
									<div className="flex-1">
										<div className="font-medium text-sm">{template.name}</div>
										<div className="text-xs text-muted-foreground">
											{metrics.totalDays} dias • {template.category}
										</div>
									</div>
									<div className="flex items-center gap-2">
										<Badge variant="outline" className="text-xs">
											{template.phases?.length || 0} fases
										</Badge>
									</div>
								</div>
							);
						})}
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<div className="space-y-6">
			{/* Cabeçalho com controles */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Folder className="h-6 w-6" />
						Gerenciamento de Templates
					</CardTitle>
					<CardDescription>
						Gerencie templates de projetos para agilizar seus cálculos
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="flex flex-wrap gap-4 items-center">
						{/* Busca */}
						<div className="flex-1 min-w-64">
							<Input
								placeholder="Buscar templates..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
							/>
						</div>

						{/* Filtro por categoria */}
						<Select value={filterCategory} onValueChange={setFilterCategory}>
							<SelectTrigger className="w-48">
								<SelectValue placeholder="Filtrar por categoria" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Todas as categorias</SelectItem>
								{categories.map(category => (
									<SelectItem key={category} value={category}>
										{category.charAt(0).toUpperCase() + category.slice(1)}
									</SelectItem>
								))}
							</SelectContent>
						</Select>

						{/* Botões de ação */}
						<div className="flex gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={() => setShowCreateForm(!showCreateForm)}
							>
								<Plus className="h-4 w-4 mr-2" />
								Criar
							</Button>

							<label className="cursor-pointer">
								<Button variant="outline" size="sm" asChild>
									<span>
										<Upload className="h-4 w-4 mr-2" />
										Importar
									</span>
								</Button>
								<input
									type="file"
									accept=".json"
									onChange={handleImportTemplate}
									className="hidden"
								/>
							</label>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Lista de templates */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{isLoading ? (
					<div className="col-span-full text-center py-8 text-muted-foreground">
						Carregando templates...
					</div>
				) : error ? (
					<div className="col-span-full text-center py-8 text-destructive">
						Erro: {error}
					</div>
				) : filteredTemplates.length === 0 ? (
					<div className="col-span-full text-center py-8 text-muted-foreground">
						Nenhum template encontrado
					</div>
				) : (
					filteredTemplates.map((template) => {
						const metrics = calculateTemplateMetrics(template);
						const isCustom = template.id.startsWith("custom_");

						return (
							<Card key={template.id} className="hover:shadow-md transition-shadow">
								<CardHeader className="pb-3">
									<div className="flex items-start justify-between">
										<div className="flex-1">
											<CardTitle className="text-lg">{template.name}</CardTitle>
											<CardDescription className="mt-1">
												{template.description}
											</CardDescription>
										</div>
										<Badge variant={isCustom ? "default" : "secondary"}>
											{isCustom ? "Personalizado" : "Padrão"}
										</Badge>
									</div>
								</CardHeader>

								<CardContent className="space-y-4">
									{/* Métricas básicas */}
									<div className="grid grid-cols-2 gap-4 text-sm">
										<div className="flex items-center gap-2">
											<Clock className="h-4 w-4 text-muted-foreground" />
											<span>{metrics.totalDays} dias</span>
										</div>
										<div className="flex items-center gap-2">
											<BarChart3 className="h-4 w-4 text-muted-foreground" />
											<span>{template.phases?.length || 0} fases</span>
										</div>
									</div>

									{/* Categoria */}
									<div>
										<Badge variant="outline" className="text-xs">
											{template.category}
										</Badge>
									</div>

									{/* Progresso das fases (se houver) */}
									{template.phases && template.phases.length > 0 && (
										<div className="space-y-2">
											<div className="text-sm font-medium">Distribuição das Fases</div>
											{metrics.phaseMetrics.slice(0, 3).map((phase) => (
												<div key={phase.id} className="space-y-1">
													<div className="flex justify-between text-xs">
														<span className="truncate">{phase.name}</span>
														<span>{phase.estimatedDays}d</span>
													</div>
													<Progress value={phase.percentage} className="h-1" />
												</div>
											))}
											{template.phases.length > 3 && (
												<div className="text-xs text-muted-foreground">
													+{template.phases.length - 3} fases adicionais
												</div>
											)}
										</div>
									)}

									{/* Status de validação */}
									<div className="flex items-center gap-2 text-sm">
										{metrics.validation.isValid ? (
											<>
												<CheckCircle className="h-4 w-4 text-green-500" />
												<span className="text-green-700">Válido</span>
											</>
										) : (
											<>
												<AlertCircle className="h-4 w-4 text-red-500" />
												<span className="text-red-700">Problemas detectados</span>
											</>
										)}
									</div>

									<Separator />

									{/* Ações */}
									<div className="flex gap-2">
										<Button
											size="sm"
											onClick={() => handleApplyTemplate(template)}
											className="flex-1"
										>
											Aplicar
										</Button>

										<Button
											variant="outline"
											size="sm"
											onClick={() => handleCloneTemplate(template)}
										>
											<Copy className="h-4 w-4" />
										</Button>

										<Button
											variant="outline"
											size="sm"
											onClick={() => exportTemplate(template)}
										>
											<Download className="h-4 w-4" />
										</Button>

										{isCustom && (
											<Button
												variant="outline"
												size="sm"
												onClick={() => deleteTemplate(template.id)}
												className="text-destructive hover:text-destructive"
											>
												<Trash2 className="h-4 w-4" />
											</Button>
										)}
									</div>
								</CardContent>
							</Card>
						);
					})
				)}
			</div>
		</div>
	);
}

import { useState, useCallback, useEffect, useMemo } from "react";
import { ITemplateManagerHook, IProjectTemplate, IWorkingDaysCalculation } from "@/types";
import { templateService } from "@/services/templateService";
import { storageService } from "@/services/storageService";
import { useNotifications } from "./useNotifications";

export function useTemplateManager(): ITemplateManagerHook {
	const [templates, setTemplates] = useState<IProjectTemplate[]>([]);
	const [selectedTemplate, setSelectedTemplate] = useState<IProjectTemplate | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const { addNotification } = useNotifications();

	// Carregar templates iniciais
	useEffect(() => {
		loadTemplates();
	}, []);

	const loadTemplates = useCallback(async () => {
		setIsLoading(true);
		setError(null);

		try {
			// Carregar templates padrão
			const defaultTemplates = templateService.getDefaultTemplates();

			// Carregar templates personalizados do localStorage
			const customTemplates = storageService.getCustomTemplates();

			// Sincronizar templates personalizados com o serviço
			// (O serviço já mantém os templates em memória)

			const allTemplates = templateService.getTemplates();
			setTemplates(allTemplates);
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Erro ao carregar templates";
			setError(errorMessage);
			addNotification({
				type: "error",
				title: "Erro ao carregar templates",
				message: errorMessage,
				duration: 5000,
			});
		} finally {
			setIsLoading(false);
		}
	}, [addNotification]);

	// Selecionar template
	const selectTemplate = useCallback(
		(id: string) => {
			const template = templateService.getTemplateById(id);
			setSelectedTemplate(template);

			if (template) {
				addNotification({
					type: "info",
					title: "Template selecionado",
					message: `Template "${template.name}" foi selecionado`,
					duration: 2000,
				});
			}
		},
		[addNotification]
	);

	// Criar novo template
	const createTemplate = useCallback(
		(template: Omit<IProjectTemplate, "id" | "createdAt" | "updatedAt">) => {
			try {
				// Validar dependências se houver fases
				if (template.phases && template.phases.length > 0) {
					const validation = templateService.validateDependencies(template as IProjectTemplate);
					if (!validation.isValid) {
						throw new Error(`Erro de validação: ${validation.errors.join(", ")}`);
					}
				}

				const newTemplate = templateService.createTemplate(template);

				// Salvar no localStorage
				storageService.saveCustomTemplate(newTemplate);

				// Atualizar lista
				setTemplates(templateService.getTemplates());

				addNotification({
					type: "success",
					title: "Template criado",
					message: `Template "${newTemplate.name}" foi criado com sucesso`,
					duration: 3000,
				});
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : "Erro ao criar template";
				setError(errorMessage);
				addNotification({
					type: "error",
					title: "Erro ao criar template",
					message: errorMessage,
					duration: 5000,
				});
			}
		},
		[addNotification]
	);

	// Atualizar template
	const updateTemplate = useCallback(
		(id: string, updates: Partial<IProjectTemplate>) => {
			try {
				const updatedTemplate = templateService.updateTemplate(id, updates);

				if (!updatedTemplate) {
					throw new Error("Template não encontrado ou não pode ser atualizado");
				}

				// Validar dependências se houver fases
				if (updatedTemplate.phases && updatedTemplate.phases.length > 0) {
					const validation = templateService.validateDependencies(updatedTemplate);
					if (!validation.isValid) {
						throw new Error(`Erro de validação: ${validation.errors.join(", ")}`);
					}
				}

				// Salvar no localStorage
				storageService.saveCustomTemplate(updatedTemplate);

				// Atualizar lista
				setTemplates(templateService.getTemplates());

				// Atualizar template selecionado se for o mesmo
				if (selectedTemplate?.id === id) {
					setSelectedTemplate(updatedTemplate);
				}

				addNotification({
					type: "success",
					title: "Template atualizado",
					message: `Template "${updatedTemplate.name}" foi atualizado`,
					duration: 3000,
				});
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : "Erro ao atualizar template";
				setError(errorMessage);
				addNotification({
					type: "error",
					title: "Erro ao atualizar template",
					message: errorMessage,
					duration: 5000,
				});
			}
		},
		[selectedTemplate, addNotification]
	);

	// Deletar template
	const deleteTemplate = useCallback(
		(id: string) => {
			try {
				const success = templateService.deleteTemplate(id);

				if (!success) {
					throw new Error("Template não pode ser deletado (pode ser um template padrão)");
				}

				// Remover do localStorage
				storageService.removeCustomTemplate(id);

				// Atualizar lista
				setTemplates(templateService.getTemplates());

				// Limpar seleção se for o template deletado
				if (selectedTemplate?.id === id) {
					setSelectedTemplate(null);
				}

				addNotification({
					type: "success",
					title: "Template deletado",
					message: "Template foi removido com sucesso",
					duration: 3000,
				});
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : "Erro ao deletar template";
				setError(errorMessage);
				addNotification({
					type: "error",
					title: "Erro ao deletar template",
					message: errorMessage,
					duration: 5000,
				});
			}
		},
		[selectedTemplate, addNotification]
	);

	// Aplicar template ao cálculo
	const applyTemplate = useCallback(
		(templateId: string): IWorkingDaysCalculation => {
			const template = templateService.getTemplateById(templateId);

			if (!template) {
				throw new Error("Template não encontrado");
			}

			const calculation: IWorkingDaysCalculation = {
				startDate: new Date(),
				workingDays: template.defaultWorkingDays,
				includeStartDate: true,
				excludeHolidays: template.excludeHolidays,
				customWorkingDays: template.workingDaysPattern,
			};

			addNotification({
				type: "success",
				title: "Template aplicado",
				message: `Configurações do template "${template.name}" foram aplicadas`,
				duration: 3000,
			});

			return calculation;
		},
		[addNotification]
	);

	return {
		templates,
		selectedTemplate,
		isLoading,
		error,
		selectTemplate,
		createTemplate,
		updateTemplate,
		deleteTemplate,
		applyTemplate,
	};
}

// Hook para análise de templates
export function useTemplateAnalysis() {
	const calculateTemplateMetrics = useCallback((template: IProjectTemplate) => {
		const totalDays = templateService.calculateTotalDays(template);
		const totalDaysWithBuffer = totalDays + template.estimatedBufferDays;
		const validation = templateService.validateDependencies(template);
		const sortedPhases = templateService.sortPhasesByDependencies(template);

		const phaseMetrics =
			template.phases?.map(phase => ({
				...phase,
				percentage: (phase.estimatedDays / totalDays) * 100,
			})) || [];

		return {
			totalDays,
			totalDaysWithBuffer,
			bufferPercentage: (template.estimatedBufferDays / totalDays) * 100,
			phaseCount: template.phases?.length || 0,
			optionalPhases: template.phases?.filter(p => p.isOptional).length || 0,
			validation,
			sortedPhases,
			phaseMetrics,
		};
	}, []);

	const compareTemplates = useCallback(
		(templates: IProjectTemplate[]) => {
			return templates
				.map(template => ({
					template,
					metrics: calculateTemplateMetrics(template),
				}))
				.sort((a, b) => a.metrics.totalDays - b.metrics.totalDays);
		},
		[calculateTemplateMetrics]
	);

	return {
		calculateTemplateMetrics,
		compareTemplates,
	};
}

// Hook para clonagem e customização de templates
export function useTemplateCustomization() {
	const { addNotification } = useNotifications();

	const cloneTemplate = useCallback(
		(templateId: string, newName: string) => {
			try {
				const clonedTemplate = templateService.cloneTemplate(templateId, newName);

				if (!clonedTemplate) {
					throw new Error("Template não encontrado para clonagem");
				}

				// Salvar no localStorage
				storageService.saveCustomTemplate(clonedTemplate);

				addNotification({
					type: "success",
					title: "Template clonado",
					message: `Template "${newName}" foi criado baseado no template original`,
					duration: 3000,
				});

				return clonedTemplate;
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : "Erro ao clonar template";
				addNotification({
					type: "error",
					title: "Erro ao clonar template",
					message: errorMessage,
					duration: 5000,
				});
				return null;
			}
		},
		[addNotification]
	);

	const exportTemplate = useCallback(
		(template: IProjectTemplate) => {
			try {
				const exportData = {
					...template,
					exportedAt: new Date().toISOString(),
					version: "1.0",
				};

				const content = JSON.stringify(exportData, null, 2);
				const blob = new Blob([content], { type: "application/json" });
				const url = URL.createObjectURL(blob);

				const link = document.createElement("a");
				link.href = url;
				link.download = `template_${template.name.replace(/\s+/g, "_").toLowerCase()}.json`;
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);
				URL.revokeObjectURL(url);

				addNotification({
					type: "success",
					title: "Template exportado",
					message: `Template "${template.name}" foi exportado com sucesso`,
					duration: 3000,
				});
			} catch (err) {
				addNotification({
					type: "error",
					title: "Erro na exportação",
					message: "Não foi possível exportar o template",
					duration: 5000,
				});
			}
		},
		[addNotification]
	);

	const importTemplate = useCallback(
		(file: File): Promise<IProjectTemplate | null> => {
			return new Promise((resolve, reject) => {
				const reader = new FileReader();

				reader.onload = e => {
					try {
						const content = e.target?.result as string;
						const templateData = JSON.parse(content);

						// Validar estrutura básica
						if (!templateData.name || !templateData.category) {
							throw new Error("Arquivo de template inválido");
						}

						// Criar novo template
						const newTemplate = templateService.createTemplate({
							name: templateData.name,
							description: templateData.description || "",
							category: templateData.category,
							defaultWorkingDays: templateData.defaultWorkingDays || 10,
							workingDaysPattern: templateData.workingDaysPattern || [1, 2, 3, 4, 5],
							excludeHolidays: templateData.excludeHolidays !== false,
							estimatedBufferDays: templateData.estimatedBufferDays || 0,
							phases: templateData.phases || [],
						});

						// Salvar no localStorage
						storageService.saveCustomTemplate(newTemplate);

						addNotification({
							type: "success",
							title: "Template importado",
							message: `Template "${newTemplate.name}" foi importado com sucesso`,
							duration: 3000,
						});

						resolve(newTemplate);
					} catch (err) {
						addNotification({
							type: "error",
							title: "Erro na importação",
							message: "Formato de arquivo inválido",
							duration: 5000,
						});
						reject(err);
					}
				};

				reader.readAsText(file);
			});
		},
		[addNotification]
	);

	return {
		cloneTemplate,
		exportTemplate,
		importTemplate,
	};
}

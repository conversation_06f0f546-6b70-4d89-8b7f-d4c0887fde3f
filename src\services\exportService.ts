import { 
	IExportService, 
	IWorkingDaysCalculation, 
	IWorkingDaysResult,
	ICalculationHistory,
	IProjectTemplate,
	IExportOptions,
	IReportData
} from "@/types";
import { format, addDays } from "date-fns";
import { ptBR } from "date-fns/locale";

class ExportService implements IExportService {

	/**
	 * Exporta dados para CSV
	 */
	async exportToCSV(data: IReportData, options: IExportOptions = {}): Promise<string> {
		const { 
			includeHeaders = true, 
			delimiter = ",",
			filename = "relatorio-dias-uteis.csv" 
		} = options;

		let csvContent = "";

		// Cabeçalhos
		if (includeHeaders) {
			const headers = [
				"Data de Início",
				"Data de Fim", 
				"Dias Úteis",
				"Dias Totais",
				"Feriados",
				"Fins de Semana",
				"Incluir Data Inicial",
				"Excluir Feriados",
				"Padrão de Trabalho",
				"Data do Cálculo"
			];
			csvContent += headers.join(delimiter) + "\n";
		}

		// Dados
		data.calculations.forEach(calc => {
			const row = [
				format(calc.calculation.startDate, "dd/MM/yyyy", { locale: ptBR }),
				format(calc.result.endDate, "dd/MM/yyyy", { locale: ptBR }),
				calc.result.workingDaysCount.toString(),
				calc.result.totalDays?.toString() || "",
				calc.result.holidaysInPeriod?.length.toString() || "0",
				calc.result.weekendsCount?.toString() || "",
				calc.calculation.includeStartDate ? "Sim" : "Não",
				calc.calculation.excludeHolidays ? "Sim" : "Não",
				this.formatWorkingDaysPattern(calc.calculation.customWorkingDays),
				format(calc.timestamp, "dd/MM/yyyy HH:mm", { locale: ptBR })
			];
			csvContent += row.map(field => `"${field}"`).join(delimiter) + "\n";
		});

		// Download do arquivo
		this.downloadFile(csvContent, filename, "text/csv");
		return csvContent;
	}

	/**
	 * Exporta dados para Excel (formato CSV compatível)
	 */
	async exportToExcel(data: IReportData, options: IExportOptions = {}): Promise<string> {
		const { filename = "relatorio-dias-uteis.xlsx" } = options;
		
		// Para uma implementação completa, seria necessário usar uma biblioteca como xlsx
		// Por enquanto, exportamos como CSV com extensão .xlsx
		const csvContent = await this.exportToCSV(data, { 
			...options, 
			filename: filename.replace('.xlsx', '.csv') 
		});
		
		return csvContent;
	}

	/**
	 * Exporta para formato iCal
	 */
	async exportToICal(data: IReportData, options: IExportOptions = {}): Promise<string> {
		const { filename = "calendario-projeto.ics" } = options;
		
		let icalContent = [
			"BEGIN:VCALENDAR",
			"VERSION:2.0",
			"PRODID:-//Contador de Dias Úteis//PT",
			"CALSCALE:GREGORIAN",
			"METHOD:PUBLISH"
		].join("\r\n") + "\r\n";

		data.calculations.forEach((calc, index) => {
			const startDate = format(calc.calculation.startDate, "yyyyMMdd");
			const endDate = format(calc.result.endDate, "yyyyMMdd");
			const now = format(new Date(), "yyyyMMdd'T'HHmmss'Z'");
			
			icalContent += [
				"BEGIN:VEVENT",
				`UID:working-days-${index}-${Date.now()}@contador-dias-uteis.com`,
				`DTSTAMP:${now}`,
				`DTSTART;VALUE=DATE:${startDate}`,
				`DTEND;VALUE=DATE:${endDate}`,
				`SUMMARY:Projeto - ${calc.result.workingDaysCount} dias úteis`,
				`DESCRIPTION:Período de trabalho calculado\\nDias úteis: ${calc.result.workingDaysCount}\\nTotal de dias: ${calc.result.totalDays || 'N/A'}\\nFeriados: ${calc.result.holidaysInPeriod?.length || 0}`,
				"STATUS:CONFIRMED",
				"TRANSP:TRANSPARENT",
				"END:VEVENT"
			].join("\r\n") + "\r\n";
		});

		icalContent += "END:VCALENDAR\r\n";

		this.downloadFile(icalContent, filename, "text/calendar");
		return icalContent;
	}

	/**
	 * Gera relatório em PDF (HTML para impressão)
	 */
	async exportToPDF(data: IReportData, options: IExportOptions = {}): Promise<string> {
		const { filename = "relatorio-dias-uteis.pdf" } = options;
		
		const htmlContent = this.generateReportHTML(data);
		
		// Para uma implementação completa, seria necessário usar uma biblioteca como jsPDF ou Puppeteer
		// Por enquanto, abrimos uma nova janela com o HTML para impressão
		const printWindow = window.open('', '_blank');
		if (printWindow) {
			printWindow.document.write(htmlContent);
			printWindow.document.close();
			printWindow.focus();
			
			// Aguardar carregamento e imprimir
			setTimeout(() => {
				printWindow.print();
			}, 500);
		}
		
		return htmlContent;
	}

	/**
	 * Exporta dados para JSON
	 */
	async exportToJSON(data: IReportData, options: IExportOptions = {}): Promise<string> {
		const { filename = "dados-calculos.json" } = options;
		
		const jsonData = {
			exportDate: new Date().toISOString(),
			summary: data.summary,
			calculations: data.calculations.map(calc => ({
				id: calc.id,
				calculation: {
					...calc.calculation,
					startDate: calc.calculation.startDate.toISOString(),
				},
				result: {
					...calc.result,
					endDate: calc.result.endDate.toISOString(),
				},
				timestamp: calc.timestamp.toISOString(),
			})),
		};
		
		const jsonContent = JSON.stringify(jsonData, null, 2);
		this.downloadFile(jsonContent, filename, "application/json");
		return jsonContent;
	}

	/**
	 * Gera relatório detalhado
	 */
	generateDetailedReport(data: IReportData): string {
		const { summary, calculations } = data;
		
		let report = "=== RELATÓRIO DETALHADO DE DIAS ÚTEIS ===\n\n";
		
		// Resumo geral
		report += "RESUMO GERAL:\n";
		report += `- Total de cálculos: ${summary.totalCalculations}\n`;
		report += `- Média de dias úteis: ${summary.averageWorkingDays}\n`;
		report += `- Período analisado: ${format(summary.periodStart, "dd/MM/yyyy", { locale: ptBR })} a ${format(summary.periodEnd, "dd/MM/yyyy", { locale: ptBR })}\n`;
		report += `- Total de dias úteis: ${summary.totalWorkingDays}\n\n`;
		
		// Detalhes por cálculo
		report += "DETALHES POR CÁLCULO:\n\n";
		calculations.forEach((calc, index) => {
			report += `${index + 1}. Cálculo realizado em ${format(calc.timestamp, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}\n`;
			report += `   - Período: ${format(calc.calculation.startDate, "dd/MM/yyyy", { locale: ptBR })} a ${format(calc.result.endDate, "dd/MM/yyyy", { locale: ptBR })}\n`;
			report += `   - Dias úteis: ${calc.result.workingDaysCount}\n`;
			report += `   - Total de dias: ${calc.result.totalDays || 'N/A'}\n`;
			report += `   - Feriados: ${calc.result.holidaysInPeriod?.length || 0}\n`;
			report += `   - Fins de semana: ${calc.result.weekendsCount || 'N/A'}\n`;
			report += `   - Configurações:\n`;
			report += `     * Incluir data inicial: ${calc.calculation.includeStartDate ? 'Sim' : 'Não'}\n`;
			report += `     * Excluir feriados: ${calc.calculation.excludeHolidays ? 'Sim' : 'Não'}\n`;
			report += `     * Padrão de trabalho: ${this.formatWorkingDaysPattern(calc.calculation.customWorkingDays)}\n\n`;
		});
		
		return report;
	}

	/**
	 * Gera HTML para relatório
	 */
	private generateReportHTML(data: IReportData): string {
		const { summary, calculations } = data;
		
		return `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Dias Úteis</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            line-height: 1.6; 
            color: #333;
        }
        .header { 
            text-align: center; 
            border-bottom: 2px solid #333; 
            padding-bottom: 20px; 
            margin-bottom: 30px; 
        }
        .summary { 
            background: #f5f5f5; 
            padding: 20px; 
            border-radius: 8px; 
            margin-bottom: 30px; 
        }
        .calculation { 
            border: 1px solid #ddd; 
            padding: 15px; 
            margin-bottom: 20px; 
            border-radius: 5px; 
        }
        .calculation h3 { 
            margin-top: 0; 
            color: #2563eb; 
        }
        .grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 15px; 
            margin: 15px 0; 
        }
        .metric { 
            background: white; 
            padding: 10px; 
            border-left: 4px solid #2563eb; 
        }
        .metric-label { 
            font-size: 12px; 
            color: #666; 
            text-transform: uppercase; 
        }
        .metric-value { 
            font-size: 18px; 
            font-weight: bold; 
            color: #333; 
        }
        @media print {
            body { margin: 0; }
            .calculation { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Relatório de Dias Úteis</h1>
        <p>Gerado em ${format(new Date(), "dd 'de' MMMM 'de' yyyy 'às' HH:mm", { locale: ptBR })}</p>
    </div>
    
    <div class="summary">
        <h2>Resumo Geral</h2>
        <div class="grid">
            <div class="metric">
                <div class="metric-label">Total de Cálculos</div>
                <div class="metric-value">${summary.totalCalculations}</div>
            </div>
            <div class="metric">
                <div class="metric-label">Média de Dias Úteis</div>
                <div class="metric-value">${summary.averageWorkingDays}</div>
            </div>
            <div class="metric">
                <div class="metric-label">Período Analisado</div>
                <div class="metric-value">${format(summary.periodStart, "dd/MM/yyyy", { locale: ptBR })} - ${format(summary.periodEnd, "dd/MM/yyyy", { locale: ptBR })}</div>
            </div>
            <div class="metric">
                <div class="metric-label">Total de Dias Úteis</div>
                <div class="metric-value">${summary.totalWorkingDays}</div>
            </div>
        </div>
    </div>
    
    <h2>Detalhes dos Cálculos</h2>
    ${calculations.map((calc, index) => `
        <div class="calculation">
            <h3>Cálculo ${index + 1} - ${format(calc.timestamp, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}</h3>
            <div class="grid">
                <div class="metric">
                    <div class="metric-label">Período</div>
                    <div class="metric-value">${format(calc.calculation.startDate, "dd/MM/yyyy", { locale: ptBR })} - ${format(calc.result.endDate, "dd/MM/yyyy", { locale: ptBR })}</div>
                </div>
                <div class="metric">
                    <div class="metric-label">Dias Úteis</div>
                    <div class="metric-value">${calc.result.workingDaysCount}</div>
                </div>
                <div class="metric">
                    <div class="metric-label">Total de Dias</div>
                    <div class="metric-value">${calc.result.totalDays || 'N/A'}</div>
                </div>
                <div class="metric">
                    <div class="metric-label">Feriados</div>
                    <div class="metric-value">${calc.result.holidaysInPeriod?.length || 0}</div>
                </div>
            </div>
            <p><strong>Configurações:</strong></p>
            <ul>
                <li>Incluir data inicial: ${calc.calculation.includeStartDate ? 'Sim' : 'Não'}</li>
                <li>Excluir feriados: ${calc.calculation.excludeHolidays ? 'Sim' : 'Não'}</li>
                <li>Padrão de trabalho: ${this.formatWorkingDaysPattern(calc.calculation.customWorkingDays)}</li>
            </ul>
        </div>
    `).join('')}
</body>
</html>`;
	}

	/**
	 * Formata padrão de dias de trabalho
	 */
	private formatWorkingDaysPattern(customWorkingDays?: number[]): string {
		if (!customWorkingDays) return "Segunda a Sexta (padrão)";
		
		const dayNames = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];
		return customWorkingDays.map(day => dayNames[day]).join(", ");
	}

	/**
	 * Faz download de arquivo
	 */
	private downloadFile(content: string, filename: string, mimeType: string): void {
		const blob = new Blob([content], { type: mimeType });
		const url = URL.createObjectURL(blob);
		
		const link = document.createElement('a');
		link.href = url;
		link.download = filename;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		
		URL.revokeObjectURL(url);
	}

	/**
	 * Prepara dados para exportação
	 */
	prepareReportData(
		calculations: ICalculationHistory[], 
		templates?: IProjectTemplate[]
	): IReportData {
		if (calculations.length === 0) {
			throw new Error("Nenhum cálculo disponível para exportação");
		}

		// Calcular resumo
		const totalCalculations = calculations.length;
		const totalWorkingDays = calculations.reduce((sum, calc) => sum + calc.result.workingDaysCount, 0);
		const averageWorkingDays = Math.round(totalWorkingDays / totalCalculations);
		
		const dates = calculations.map(calc => calc.calculation.startDate);
		const endDates = calculations.map(calc => calc.result.endDate);
		
		const periodStart = new Date(Math.min(...dates.map(d => d.getTime())));
		const periodEnd = new Date(Math.max(...endDates.map(d => d.getTime())));

		return {
			summary: {
				totalCalculations,
				averageWorkingDays,
				totalWorkingDays,
				periodStart,
				periodEnd,
				exportDate: new Date(),
			},
			calculations,
			templates: templates || [],
		};
	}
}

export const exportService = new ExportService();

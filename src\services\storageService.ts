import { 
	IStorageService, 
	IUserPreferences, 
	ICalculationHistory, 
	IProjectTemplate,
	IHoliday,
	IHolidayConfiguration 
} from "@/types";

const STORAGE_KEYS = {
	PREFERENCES: "working-days-preferences",
	HISTORY: "working-days-history",
	CUSTOM_TEMPLATES: "working-days-custom-templates",
	CUSTOM_HOLIDAYS: "working-days-custom-holidays",
	HOLIDAY_CONFIG: "working-days-holiday-config",
} as const;

const DEFAULT_PREFERENCES: IUserPreferences = {
	theme: "system",
	language: "pt-BR",
	dateFormat: "dd/MM/yyyy",
	defaultWorkingDays: [1, 2, 3, 4, 5], // Segunda a Sexta
	defaultExcludeHolidays: true,
	defaultIncludeStartDate: true,
	autoSaveCalculations: true,
	maxHistoryItems: 50,
};

const DEFAULT_HOLIDAY_CONFIG: IHolidayConfiguration = {
	enabledTypes: ["nacional", "estadual", "municipal", "personalizado"],
	customHolidays: [],
	region: "BR",
	includeRegionalHolidays: true,
};

class StorageService implements IStorageService {
	private isClient = typeof window !== "undefined";

	private getFromStorage<T>(key: string, defaultValue: T): T {
		if (!this.isClient) return defaultValue;
		
		try {
			const item = localStorage.getItem(key);
			return item ? JSON.parse(item) : defaultValue;
		} catch (error) {
			console.warn(`Erro ao ler ${key} do localStorage:`, error);
			return defaultValue;
		}
	}

	private saveToStorage<T>(key: string, value: T): void {
		if (!this.isClient) return;
		
		try {
			localStorage.setItem(key, JSON.stringify(value));
		} catch (error) {
			console.error(`Erro ao salvar ${key} no localStorage:`, error);
		}
	}

	// ============================================================================
	// PREFERÊNCIAS
	// ============================================================================

	getPreferences(): IUserPreferences {
		return this.getFromStorage(STORAGE_KEYS.PREFERENCES, DEFAULT_PREFERENCES);
	}

	updatePreferences(preferences: Partial<IUserPreferences>): void {
		const current = this.getPreferences();
		const updated = { ...current, ...preferences };
		this.saveToStorage(STORAGE_KEYS.PREFERENCES, updated);
	}

	// ============================================================================
	// HISTÓRICO
	// ============================================================================

	getHistory(): ICalculationHistory[] {
		const history = this.getFromStorage<ICalculationHistory[]>(STORAGE_KEYS.HISTORY, []);
		
		// Converter strings de data de volta para objetos Date
		return history.map(item => ({
			...item,
			timestamp: new Date(item.timestamp),
			calculation: {
				...item.calculation,
				startDate: new Date(item.calculation.startDate),
			},
			result: {
				...item.result,
				endDate: new Date(item.result.endDate),
			},
		}));
	}

	addToHistory(item: Omit<ICalculationHistory, "id" | "timestamp">): void {
		const history = this.getHistory();
		const preferences = this.getPreferences();
		
		const newItem: ICalculationHistory = {
			id: `calc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			timestamp: new Date(),
			...item,
		};

		// Adicionar no início da lista
		history.unshift(newItem);

		// Manter apenas o número máximo de itens
		const trimmedHistory = history.slice(0, preferences.maxHistoryItems);
		
		this.saveToStorage(STORAGE_KEYS.HISTORY, trimmedHistory);
	}

	removeFromHistory(id: string): void {
		const history = this.getHistory();
		const filtered = history.filter(item => item.id !== id);
		this.saveToStorage(STORAGE_KEYS.HISTORY, filtered);
	}

	clearHistory(): void {
		this.saveToStorage(STORAGE_KEYS.HISTORY, []);
	}

	// ============================================================================
	// TEMPLATES PERSONALIZADOS
	// ============================================================================

	getCustomTemplates(): IProjectTemplate[] {
		const templates = this.getFromStorage<IProjectTemplate[]>(STORAGE_KEYS.CUSTOM_TEMPLATES, []);
		
		// Converter strings de data de volta para objetos Date
		return templates.map(template => ({
			...template,
			createdAt: new Date(template.createdAt),
			updatedAt: new Date(template.updatedAt),
		}));
	}

	saveCustomTemplate(template: IProjectTemplate): void {
		const templates = this.getCustomTemplates();
		const existingIndex = templates.findIndex(t => t.id === template.id);
		
		if (existingIndex >= 0) {
			templates[existingIndex] = { ...template, updatedAt: new Date() };
		} else {
			templates.push(template);
		}
		
		this.saveToStorage(STORAGE_KEYS.CUSTOM_TEMPLATES, templates);
	}

	removeCustomTemplate(id: string): void {
		const templates = this.getCustomTemplates();
		const filtered = templates.filter(template => template.id !== id);
		this.saveToStorage(STORAGE_KEYS.CUSTOM_TEMPLATES, filtered);
	}

	// ============================================================================
	// FERIADOS PERSONALIZADOS
	// ============================================================================

	getCustomHolidays(): IHoliday[] {
		return this.getFromStorage<IHoliday[]>(STORAGE_KEYS.CUSTOM_HOLIDAYS, []);
	}

	saveCustomHolidays(holidays: IHoliday[]): void {
		this.saveToStorage(STORAGE_KEYS.CUSTOM_HOLIDAYS, holidays);
	}

	// ============================================================================
	// CONFIGURAÇÕES DE FERIADOS
	// ============================================================================

	getHolidayConfiguration(): IHolidayConfiguration {
		return this.getFromStorage(STORAGE_KEYS.HOLIDAY_CONFIG, DEFAULT_HOLIDAY_CONFIG);
	}

	saveHolidayConfiguration(config: IHolidayConfiguration): void {
		this.saveToStorage(STORAGE_KEYS.HOLIDAY_CONFIG, config);
	}

	// ============================================================================
	// UTILITÁRIOS
	// ============================================================================

	/**
	 * Exporta todos os dados para backup
	 */
	exportAllData(): string {
		const data = {
			preferences: this.getPreferences(),
			history: this.getHistory(),
			customTemplates: this.getCustomTemplates(),
			customHolidays: this.getCustomHolidays(),
			holidayConfiguration: this.getHolidayConfiguration(),
			exportedAt: new Date().toISOString(),
		};
		
		return JSON.stringify(data, null, 2);
	}

	/**
	 * Importa dados de backup
	 */
	importAllData(jsonData: string): boolean {
		try {
			const data = JSON.parse(jsonData);
			
			if (data.preferences) {
				this.saveToStorage(STORAGE_KEYS.PREFERENCES, data.preferences);
			}
			
			if (data.history) {
				this.saveToStorage(STORAGE_KEYS.HISTORY, data.history);
			}
			
			if (data.customTemplates) {
				this.saveToStorage(STORAGE_KEYS.CUSTOM_TEMPLATES, data.customTemplates);
			}
			
			if (data.customHolidays) {
				this.saveToStorage(STORAGE_KEYS.CUSTOM_HOLIDAYS, data.customHolidays);
			}
			
			if (data.holidayConfiguration) {
				this.saveToStorage(STORAGE_KEYS.HOLIDAY_CONFIG, data.holidayConfiguration);
			}
			
			return true;
		} catch (error) {
			console.error("Erro ao importar dados:", error);
			return false;
		}
	}

	/**
	 * Limpa todos os dados armazenados
	 */
	clearAllData(): void {
		if (!this.isClient) return;
		
		Object.values(STORAGE_KEYS).forEach(key => {
			localStorage.removeItem(key);
		});
	}

	/**
	 * Verifica o tamanho dos dados armazenados
	 */
	getStorageSize(): { used: number; available: number } {
		if (!this.isClient) return { used: 0, available: 0 };
		
		let used = 0;
		Object.values(STORAGE_KEYS).forEach(key => {
			const item = localStorage.getItem(key);
			if (item) {
				used += item.length;
			}
		});
		
		// Estimativa do espaço disponível (5MB é o limite típico)
		const available = 5 * 1024 * 1024 - used;
		
		return { used, available };
	}
}

export const storageService = new StorageService();

"use client";

import React, { useState } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { 
	Download, 
	FileText, 
	FileSpreadsheet, 
	Calendar, 
	FileJson,
	Printer,
	Package,
	CheckCircle,
	AlertCircle,
	Info
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { useExport, useReports } from "@/hooks/useExport";
import { ICalculationHistory, IProjectTemplate } from "@/types";

interface IExportManagerProps {
	calculations: ICalculationHistory[];
	templates?: IProjectTemplate[];
	onExportComplete?: (format: string, success: boolean) => void;
}

export function ExportManager({
	calculations,
	templates = [],
	onExportComplete
}: IExportManagerProps) {
	const [selectedFormats, setSelectedFormats] = useState<string[]>(["csv"]);
	const [customFilename, setCustomFilename] = useState("");
	const [includeTemplates, setIncludeTemplates] = useState(false);
	
	const {
		isExporting,
		error,
		lastExportData,
		exportToCSV,
		exportToExcel,
		exportToPDF,
		exportToICal,
		exportToJSON,
		exportMultipleFormats,
		clearExportState
	} = useExport();

	const {
		generateDetailedReport,
		generateProductivityReport
	} = useReports();

	// Formatos disponíveis
	const exportFormats = [
		{
			id: "csv",
			name: "CSV",
			description: "Planilha compatível com Excel",
			icon: FileSpreadsheet,
			extension: ".csv",
			color: "text-green-600"
		},
		{
			id: "excel",
			name: "Excel",
			description: "Arquivo Excel nativo",
			icon: FileSpreadsheet,
			extension: ".xlsx",
			color: "text-green-600"
		},
		{
			id: "pdf",
			name: "PDF",
			description: "Relatório para impressão",
			icon: FileText,
			extension: ".pdf",
			color: "text-red-600"
		},
		{
			id: "ical",
			name: "iCal",
			description: "Calendário para importação",
			icon: Calendar,
			extension: ".ics",
			color: "text-blue-600"
		},
		{
			id: "json",
			name: "JSON",
			description: "Dados estruturados",
			icon: FileJson,
			extension: ".json",
			color: "text-purple-600"
		}
	];

	// Manipular seleção de formatos
	const handleFormatToggle = (formatId: string) => {
		setSelectedFormats(prev => 
			prev.includes(formatId)
				? prev.filter(id => id !== formatId)
				: [...prev, formatId]
		);
	};

	// Executar exportação
	const handleExport = async (formatId: string) => {
		if (calculations.length === 0) {
			onExportComplete?.(formatId, false);
			return;
		}

		const filename = customFilename || `relatorio-${format(new Date(), "yyyy-MM-dd-HHmm")}`;
		const options = { filename };

		try {
			switch (formatId) {
				case "csv":
					await exportToCSV(calculations, options);
					break;
				case "excel":
					await exportToExcel(calculations, options);
					break;
				case "pdf":
					await exportToPDF(calculations, options);
					break;
				case "ical":
					await exportToICal(calculations, options);
					break;
				case "json":
					await exportToJSON(calculations, options);
					break;
			}
			onExportComplete?.(formatId, true);
		} catch (err) {
			onExportComplete?.(formatId, false);
		}
	};

	// Exportar múltiplos formatos
	const handleMultipleExport = async () => {
		if (selectedFormats.length === 0 || calculations.length === 0) return;

		const filename = customFilename || `relatorio-${format(new Date(), "yyyy-MM-dd-HHmm")}`;
		const options = { filename };

		try {
			await exportMultipleFormats(
				calculations, 
				selectedFormats as Array<"csv" | "excel" | "pdf" | "ical" | "json">, 
				options
			);
			onExportComplete?.("multiple", true);
		} catch (err) {
			onExportComplete?.("multiple", false);
		}
	};

	// Gerar relatório detalhado
	const handleDetailedReport = async () => {
		try {
			const report = await generateDetailedReport(
				calculations, 
				includeTemplates ? templates : undefined
			);
			
			// Exibir relatório em nova janela
			const reportWindow = window.open('', '_blank');
			if (reportWindow) {
				reportWindow.document.write(`
					<html>
						<head>
							<title>Relatório Detalhado</title>
							<style>
								body { font-family: monospace; white-space: pre-wrap; padding: 20px; }
							</style>
						</head>
						<body>${report}</body>
					</html>
				`);
				reportWindow.document.close();
			}
			
			onExportComplete?.("detailed", true);
		} catch (err) {
			onExportComplete?.("detailed", false);
		}
	};

	// Estatísticas dos dados
	const dataStats = {
		totalCalculations: calculations.length,
		dateRange: calculations.length > 0 ? {
			start: new Date(Math.min(...calculations.map(c => c.timestamp.getTime()))),
			end: new Date(Math.max(...calculations.map(c => c.timestamp.getTime())))
		} : null,
		totalWorkingDays: calculations.reduce((sum, calc) => sum + calc.result.workingDaysCount, 0),
		averageWorkingDays: calculations.length > 0 
			? Math.round(calculations.reduce((sum, calc) => sum + calc.result.workingDaysCount, 0) / calculations.length)
			: 0
	};

	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Download className="h-6 w-6" />
					Exportação e Relatórios
				</CardTitle>
				<CardDescription>
					Exporte seus dados em diferentes formatos ou gere relatórios detalhados
				</CardDescription>
			</CardHeader>

			<CardContent className="space-y-6">
				{/* Estatísticas dos dados */}
				<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
					<div className="text-center p-3 bg-muted/50 rounded-lg">
						<div className="text-2xl font-bold">{dataStats.totalCalculations}</div>
						<div className="text-sm text-muted-foreground">Cálculos</div>
					</div>
					<div className="text-center p-3 bg-muted/50 rounded-lg">
						<div className="text-2xl font-bold text-blue-600">{dataStats.totalWorkingDays}</div>
						<div className="text-sm text-muted-foreground">Dias úteis totais</div>
					</div>
					<div className="text-center p-3 bg-muted/50 rounded-lg">
						<div className="text-2xl font-bold text-green-600">{dataStats.averageWorkingDays}</div>
						<div className="text-sm text-muted-foreground">Média por projeto</div>
					</div>
					<div className="text-center p-3 bg-muted/50 rounded-lg">
						<div className="text-2xl font-bold">{templates.length}</div>
						<div className="text-sm text-muted-foreground">Templates</div>
					</div>
				</div>

				{dataStats.dateRange && (
					<Alert>
						<Info className="h-4 w-4" />
						<AlertTitle>Período dos Dados</AlertTitle>
						<AlertDescription>
							Dados de {format(dataStats.dateRange.start, "dd/MM/yyyy", { locale: ptBR })} até {format(dataStats.dateRange.end, "dd/MM/yyyy", { locale: ptBR })}
						</AlertDescription>
					</Alert>
				)}

				{/* Configurações de exportação */}
				<div className="space-y-4">
					<div>
						<Label htmlFor="filename">Nome do arquivo (opcional)</Label>
						<Input
							id="filename"
							placeholder="relatorio-dias-uteis"
							value={customFilename}
							onChange={(e) => setCustomFilename(e.target.value)}
							className="mt-1"
						/>
						<p className="text-sm text-muted-foreground mt-1">
							Se não especificado, será usado um nome com data e hora
						</p>
					</div>

					<div className="flex items-center space-x-2">
						<Checkbox
							id="include-templates"
							checked={includeTemplates}
							onCheckedChange={(checked) => setIncludeTemplates(checked as boolean)}
						/>
						<Label htmlFor="include-templates">
							Incluir informações dos templates nos relatórios
						</Label>
					</div>
				</div>

				<Separator />

				{/* Formatos de exportação */}
				<div className="space-y-4">
					<div>
						<h3 className="text-lg font-semibold mb-3">Formatos de Exportação</h3>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{exportFormats.map((format) => {
								const Icon = format.icon;
								const isSelected = selectedFormats.includes(format.id);
								
								return (
									<div
										key={format.id}
										className={cn(
											"flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-colors",
											isSelected && "border-primary bg-primary/5"
										)}
										onClick={() => handleFormatToggle(format.id)}
									>
										<div className="flex items-center gap-3">
											<Checkbox
												checked={isSelected}
												onChange={() => handleFormatToggle(format.id)}
											/>
											<Icon className={cn("h-5 w-5", format.color)} />
											<div>
												<div className="font-medium">{format.name}</div>
												<div className="text-sm text-muted-foreground">
													{format.description}
												</div>
											</div>
										</div>
										<div className="flex items-center gap-2">
											<Badge variant="outline">{format.extension}</Badge>
											<Button
												size="sm"
												variant="outline"
												onClick={(e) => {
													e.stopPropagation();
													handleExport(format.id);
												}}
												disabled={isExporting || calculations.length === 0}
											>
												<Download className="h-4 w-4" />
											</Button>
										</div>
									</div>
								);
							})}
						</div>
					</div>

					{/* Ações de exportação */}
					<div className="flex flex-wrap gap-3">
						<Button
							onClick={handleMultipleExport}
							disabled={selectedFormats.length === 0 || isExporting || calculations.length === 0}
							className="flex items-center gap-2"
						>
							<Package className="h-4 w-4" />
							Exportar Selecionados ({selectedFormats.length})
						</Button>

						<Button
							variant="outline"
							onClick={handleDetailedReport}
							disabled={isExporting || calculations.length === 0}
							className="flex items-center gap-2"
						>
							<Printer className="h-4 w-4" />
							Relatório Detalhado
						</Button>

						<Button
							variant="outline"
							onClick={clearExportState}
							disabled={isExporting}
						>
							Limpar
						</Button>
					</div>
				</div>

				{/* Estado de carregamento */}
				{isExporting && (
					<Alert>
						<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
						<AlertTitle>Exportando...</AlertTitle>
						<AlertDescription>
							Processando dados para exportação. Aguarde...
						</AlertDescription>
					</Alert>
				)}

				{/* Erro */}
				{error && (
					<Alert variant="destructive">
						<AlertCircle className="h-4 w-4" />
						<AlertTitle>Erro na Exportação</AlertTitle>
						<AlertDescription>{error}</AlertDescription>
					</Alert>
				)}

				{/* Última exportação */}
				{lastExportData && !isExporting && (
					<Alert>
						<CheckCircle className="h-4 w-4 text-green-600" />
						<AlertTitle>Última Exportação</AlertTitle>
						<AlertDescription>
							{lastExportData.summary.totalCalculations} cálculos exportados em {format(lastExportData.summary.exportDate, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
						</AlertDescription>
					</Alert>
				)}

				{/* Aviso se não há dados */}
				{calculations.length === 0 && (
					<Alert>
						<AlertCircle className="h-4 w-4" />
						<AlertTitle>Nenhum Dado Disponível</AlertTitle>
						<AlertDescription>
							Realize alguns cálculos primeiro para poder exportar os dados.
						</AlertDescription>
					</Alert>
				)}
			</CardContent>
		</Card>
	);
}

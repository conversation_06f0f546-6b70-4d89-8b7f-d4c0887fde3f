import { useState, useCallback, useMemo } from "react";
import {
	IAdvancedCalculationsHook,
	IWorkingDaysCalculation,
	IScenarioComparison,
	IPeriodAnalysis,
	IEstimateAnalysis,
	IProjectTemplate,
} from "@/types";
import { advancedCalculationService } from "@/services/advancedCalculationService";
import { useNotifications } from "./useNotifications";

export function useAdvancedCalculations(): IAdvancedCalculationsHook {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [scenarioComparison, setScenarioComparison] = useState<IScenarioComparison | null>(null);
	const [periodAnalysis, setPeriodAnalysis] = useState<IPeriodAnalysis | null>(null);
	const [estimateAnalysis, setEstimateAnalysis] = useState<IEstimateAnalysis | null>(null);

	const { addNotification } = useNotifications();

	// Comparar cenários
	const compareScenarios = useCallback(
		async (scenarios: IWorkingDaysCalculation[]) => {
			if (scenarios.length < 2) {
				setError("É necessário pelo menos 2 cenários para comparação");
				return;
			}

			setIsLoading(true);
			setError(null);

			try {
				const comparison = advancedCalculationService.compareScenarios(scenarios);
				setScenarioComparison(comparison);

				addNotification({
					type: "success",
					title: "Comparação concluída",
					message: `${scenarios.length} cenários foram comparados com sucesso`,
					duration: 3000,
				});
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : "Erro ao comparar cenários";
				setError(errorMessage);
				addNotification({
					type: "error",
					title: "Erro na comparação",
					message: errorMessage,
					duration: 5000,
				});
			} finally {
				setIsLoading(false);
			}
		},
		[addNotification]
	);

	// Analisar período
	const analyzePeriod = useCallback(
		async (startDate: Date, endDate: Date, workingDaysPattern?: number[]) => {
			if (startDate >= endDate) {
				setError("Data de início deve ser anterior à data de fim");
				return;
			}

			setIsLoading(true);
			setError(null);

			try {
				const analysis = advancedCalculationService.analyzePeriod(startDate, endDate, workingDaysPattern);
				setPeriodAnalysis(analysis);

				addNotification({
					type: "success",
					title: "Análise concluída",
					message: "Período foi analisado com sucesso",
					duration: 3000,
				});
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : "Erro ao analisar período";
				setError(errorMessage);
				addNotification({
					type: "error",
					title: "Erro na análise",
					message: errorMessage,
					duration: 5000,
				});
			} finally {
				setIsLoading(false);
			}
		},
		[addNotification]
	);

	// Gerar estimativas
	const generateEstimates = useCallback(
		async (baseCalculation: IWorkingDaysCalculation, bufferPercentages?: number[]) => {
			setIsLoading(true);
			setError(null);

			try {
				const estimates = advancedCalculationService.generateEstimates(baseCalculation, bufferPercentages);
				setEstimateAnalysis(estimates);

				addNotification({
					type: "success",
					title: "Estimativas geradas",
					message: "Análise de estimativas foi concluída",
					duration: 3000,
				});
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : "Erro ao gerar estimativas";
				setError(errorMessage);
				addNotification({
					type: "error",
					title: "Erro nas estimativas",
					message: errorMessage,
					duration: 5000,
				});
			} finally {
				setIsLoading(false);
			}
		},
		[addNotification]
	);

	// Calcular estimativa baseada em template
	const calculateTemplateEstimate = useCallback(
		async (template: IProjectTemplate, startDate: Date) => {
			setIsLoading(true);
			setError(null);

			try {
				const estimates = advancedCalculationService.calculateTemplateEstimate(template, startDate);
				setEstimateAnalysis(estimates);

				addNotification({
					type: "success",
					title: "Estimativa do template calculada",
					message: `Estimativas baseadas no template "${template.name}" foram geradas`,
					duration: 3000,
				});
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : "Erro ao calcular estimativa do template";
				setError(errorMessage);
				addNotification({
					type: "error",
					title: "Erro na estimativa do template",
					message: errorMessage,
					duration: 5000,
				});
			} finally {
				setIsLoading(false);
			}
		},
		[addNotification]
	);

	// Limpar resultados
	const clearResults = useCallback(() => {
		setScenarioComparison(null);
		setPeriodAnalysis(null);
		setEstimateAnalysis(null);
		setError(null);
	}, []);

	return {
		isLoading,
		error,
		scenarioComparison,
		periodAnalysis,
		estimateAnalysis,
		compareScenarios,
		analyzePeriod,
		generateEstimates,
		calculateTemplateEstimate,
		clearResults,
	};
}

// Hook para análise de cenários específicos
export function useScenarioAnalysis() {
	const { compareScenarios, scenarioComparison, isLoading, error } = useAdvancedCalculations();

	// Criar cenários pré-definidos
	const createCommonScenarios = useCallback((baseCalculation: IWorkingDaysCalculation) => {
		const scenarios: IWorkingDaysCalculation[] = [
			// Cenário otimista (sem feriados, todos os dias úteis)
			{
				...baseCalculation,
				excludeHolidays: false,
				customWorkingDays: [1, 2, 3, 4, 5, 6], // Incluindo sábado
			},
			// Cenário realista (padrão)
			{
				...baseCalculation,
			},
			// Cenário conservador (com buffer extra)
			{
				...baseCalculation,
				workingDays: Math.ceil(baseCalculation.workingDays * 1.2), // 20% a mais
			},
			// Cenário pessimista (apenas 4 dias por semana)
			{
				...baseCalculation,
				customWorkingDays: [1, 2, 3, 4], // Segunda a quinta
				workingDays: Math.ceil(baseCalculation.workingDays * 1.3), // 30% a mais
			},
		];

		return scenarios;
	}, []);

	const analyzeCommonScenarios = useCallback(
		async (baseCalculation: IWorkingDaysCalculation) => {
			const scenarios = createCommonScenarios(baseCalculation);
			await compareScenarios(scenarios);
		},
		[createCommonScenarios, compareScenarios]
	);

	return {
		scenarioComparison,
		isLoading,
		error,
		createCommonScenarios,
		analyzeCommonScenarios,
		compareScenarios,
	};
}

// Hook para análise de riscos
export function useRiskAnalysis() {
	const { generateEstimates, estimateAnalysis, isLoading, error } = useAdvancedCalculations();

	// Analisar riscos do projeto
	const analyzeProjectRisks = useCallback((calculation: IWorkingDaysCalculation) => {
		const risks: Array<{ factor: string; impact: "low" | "medium" | "high"; description: string }> = [];

		// Verificar duração do projeto
		if (calculation.workingDays > 60) {
			risks.push({
				factor: "Duração longa",
				impact: "high",
				description: "Projetos longos têm maior incerteza e risco de mudanças de escopo",
			});
		}

		// Verificar padrão de trabalho
		if (calculation.customWorkingDays && calculation.customWorkingDays.length < 5) {
			risks.push({
				factor: "Padrão de trabalho reduzido",
				impact: "medium",
				description: "Menos dias de trabalho por semana podem afetar a produtividade",
			});
		}

		// Verificar período do ano
		const month = calculation.startDate.getMonth();
		if ([11, 0, 6].includes(month)) {
			// Dezembro, Janeiro, Julho
			risks.push({
				factor: "Período de férias",
				impact: "medium",
				description: "Período inclui épocas típicas de férias e feriados",
			});
		}

		// Verificar se exclui feriados
		if (!calculation.excludeHolidays) {
			risks.push({
				factor: "Feriados incluídos",
				impact: "low",
				description: "Incluir feriados como dias úteis pode ser irrealista",
			});
		}

		return risks;
	}, []);

	// Gerar recomendações baseadas nos riscos
	const generateRiskRecommendations = useCallback((risks: ReturnType<typeof analyzeProjectRisks>) => {
		const recommendations: string[] = [];

		const highRisks = risks.filter(r => r.impact === "high");
		const mediumRisks = risks.filter(r => r.impact === "medium");

		if (highRisks.length > 0) {
			recommendations.push("Considere adicionar buffer de 25-40% devido aos riscos altos identificados");
		}

		if (mediumRisks.length > 1) {
			recommendations.push("Múltiplos riscos médios detectados - adicione buffer de 15-25%");
		}

		if (risks.some(r => r.factor === "Duração longa")) {
			recommendations.push("Para projetos longos, considere marcos intermediários e revisões periódicas");
		}

		if (risks.some(r => r.factor === "Período de férias")) {
			recommendations.push("Considere reagendar ou adicionar tempo extra para períodos de férias");
		}

		return recommendations;
	}, []);

	return {
		estimateAnalysis,
		isLoading,
		error,
		analyzeProjectRisks,
		generateRiskRecommendations,
		generateEstimates,
	};
}

// Hook para comparação de templates
export function useTemplateComparison() {
	const { compareScenarios, scenarioComparison, isLoading, error } = useAdvancedCalculations();

	const compareTemplates = useCallback(
		async (templates: IProjectTemplate[], startDate: Date) => {
			const scenarios: IWorkingDaysCalculation[] = templates.map(template => ({
				startDate,
				workingDays: template.defaultWorkingDays,
				includeStartDate: true,
				excludeHolidays: template.excludeHolidays,
				customWorkingDays: template.workingDaysPattern,
			}));

			await compareScenarios(scenarios);
		},
		[compareScenarios]
	);

	// Análise específica para templates
	const templateAnalysis = useMemo(() => {
		if (!scenarioComparison) return null;

		return {
			...scenarioComparison,
			templateInsights: {
				mostEfficient: scenarioComparison.fastest,
				leastEfficient: scenarioComparison.slowest,
				averageDuration: scenarioComparison.statistics.average,
				variability: scenarioComparison.statistics.standardDeviation,
			},
		};
	}, [scenarioComparison]);

	return {
		templateAnalysis,
		isLoading,
		error,
		compareTemplates,
	};
}
